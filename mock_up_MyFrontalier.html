<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>MyFrontaliers Web Kit</title>
    <link href="https://fonts.googleapis.com" rel="preconnect"/>
    <link crossorigin="" href="https://fonts.gstatic.com" rel="preconnect"/>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600&display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.tailwindcss.com?plugins=forms,container-queries"></script>
    <style type="text/tailwindcss">
        :root {
            --color-primary: #1a202c;
            --color-secondary: #2d3748;
            --color-accent: #3182ce;
            --color-success: #38a169;
            --color-success-light: #68d391;
            --color-warning: #ed8936;
            --color-error: #e53e3e;
            --color-background: #ffffff;
            --color-background-secondary: #f7fafc;
            --color-background-tertiary: #edf2f7;
            --color-text-primary: #1a202c;
            --color-text-secondary: #4a5568;
            --color-text-muted: #718096;
            --color-border: #e2e8f0;
            --color-border-light: #f1f5f9;
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            --status-todo: #ed8936;
            --status-inprogress: #3182ce;
            --status-waiting: #d69e2e;
            --status-completed: #38a169;
        }
        * {
            box-sizing: border-box;
        }

        body {
            font-family: "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
            background-color: var(--color-background);
            color: var(--color-text-primary);
            line-height: 1.6;
            font-weight: 400;
            letter-spacing: -0.01em;
        }

        .font-display {
            font-family: "Playfair Display", Georgia, serif;
        }

        .premium-card {
            background: var(--color-background);
            border: 1px solid var(--color-border-light);
            border-radius: 12px;
            box-shadow: var(--shadow-sm);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .premium-card:hover {
            box-shadow: var(--shadow-md);
            transform: translateY(-1px);
        }

        .premium-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0.75rem 1.5rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            letter-spacing: 0.025em;
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
            border: none;
            cursor: pointer;
            text-decoration: none;
        }

        .premium-button-primary {
            background-color: var(--color-primary);
            color: white;
        }

        .premium-button-primary:hover {
            background-color: var(--color-secondary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .premium-button-secondary {
            background-color: var(--color-background);
            color: var(--color-text-secondary);
            border: 1px solid var(--color-border);
        }

        .premium-button-secondary:hover {
            background-color: var(--color-background-secondary);
            border-color: var(--color-accent);
            color: var(--color-accent);
        }

        .premium-button-accent {
            background-color: var(--color-accent);
            color: white;
        }

        .premium-button-accent:hover {
            background-color: #2c5aa0;
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.375rem 0.875rem;
            border-radius: 20px;
            font-weight: 500;
            font-size: 0.75rem;
            text-transform: capitalize;
            letter-spacing: 0.025em;
        }

        .status-todo {
            background-color: rgba(237, 137, 54, 0.1);
            color: var(--status-todo);
            border: 1px solid rgba(237, 137, 54, 0.2);
        }

        .status-inprogress {
            background-color: rgba(49, 130, 206, 0.1);
            color: var(--status-inprogress);
            border: 1px solid rgba(49, 130, 206, 0.2);
        }

        .status-waiting {
            background-color: rgba(214, 158, 46, 0.1);
            color: var(--status-waiting);
            border: 1px solid rgba(214, 158, 46, 0.2);
        }

        .status-completed {
            background-color: rgba(56, 161, 105, 0.1);
            color: var(--status-completed);
            border: 1px solid rgba(56, 161, 105, 0.2);
        }

        .sidebar-nav-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.875rem 1rem;
            border-radius: 10px;
            color: var(--color-text-secondary);
            text-decoration: none;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            margin-bottom: 0.375rem;
            position: relative;
            border: 1px solid transparent;
            cursor: pointer;
        }

        .sidebar-nav-item:hover {
            background-color: var(--color-background-secondary);
            color: var(--color-accent);
            transform: translateX(4px);
            border-color: rgba(49, 130, 206, 0.15);
            box-shadow: 0 2px 8px rgba(49, 130, 206, 0.1);
        }

        .sidebar-nav-item:hover svg {
            transform: scale(1.1);
        }

        .sidebar-nav-item svg {
            transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .sidebar-nav-item.active {
            background: linear-gradient(135deg, rgba(49, 130, 206, 0.12) 0%, rgba(49, 130, 206, 0.08) 100%);
            color: var(--color-accent);
            border: 1px solid rgba(49, 130, 206, 0.25);
            font-weight: 600;
            box-shadow: 0 4px 12px rgba(49, 130, 206, 0.15);
            transform: translateX(6px);
            z-index: 25; /* Ensure active items appear above the separator */
        }

        .sidebar-nav-item.active::before {
            content: '';
            position: absolute;
            left: -1px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 24px;
            background: linear-gradient(180deg, var(--color-accent) 0%, #2c5aa0 100%);
            border-radius: 0 2px 2px 0;
            box-shadow: 0 2px 4px rgba(49, 130, 206, 0.3);
        }

        .sidebar-nav-item.active svg {
            color: var(--color-accent);
            transform: scale(1.05);
        }

        .sidebar-nav-item:active {
            transform: translateX(2px);
            transition: transform 0.1s ease;
        }

        .tab-content {
            display: none;
            opacity: 0;
            transform: translateY(10px);
            transition: opacity 0.3s ease, transform 0.3s ease;
        }

        .tab-content.active {
            display: block;
            opacity: 1;
            transform: translateY(0);
            animation: fadeInUp 0.4s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Amélioration de l'accessibilité */
        .sidebar-nav-item:focus {
            outline: 2px solid var(--color-accent);
            outline-offset: 2px;
            background-color: var(--color-background-secondary);
        }

        .sidebar-nav-item:focus:not(:focus-visible) {
            outline: none;
        }

        /* Elegant Sidebar Separator */
        .sidebar-container::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 1px;
            height: 100%;
            background: linear-gradient(
                180deg,
                transparent 0%,
                rgba(49, 130, 206, 0.1) 10%,
                rgba(49, 130, 206, 0.15) 30%,
                rgba(49, 130, 206, 0.2) 50%,
                rgba(49, 130, 206, 0.15) 70%,
                rgba(49, 130, 206, 0.1) 90%,
                transparent 100%
            );
            z-index: 20;
        }

        .sidebar-container::before {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            width: 3px;
            height: 100%;
            background: linear-gradient(
                180deg,
                transparent 0%,
                rgba(49, 130, 206, 0.03) 20%,
                rgba(49, 130, 206, 0.06) 50%,
                rgba(49, 130, 206, 0.03) 80%,
                transparent 100%
            );
            filter: blur(1px);
            z-index: 15;
        }

        /* Enhanced shadow for the sidebar */
        .sidebar-container {
            box-shadow:
                2px 0 8px rgba(49, 130, 206, 0.04),
                1px 0 3px rgba(49, 130, 206, 0.06),
                0 0 1px rgba(49, 130, 206, 0.08);
        }

        /* Subtle glow effect on sidebar hover interactions */
        .sidebar-container:hover::after {
            background: linear-gradient(
                180deg,
                transparent 0%,
                rgba(49, 130, 206, 0.15) 10%,
                rgba(49, 130, 206, 0.25) 30%,
                rgba(49, 130, 206, 0.3) 50%,
                rgba(49, 130, 206, 0.25) 70%,
                rgba(49, 130, 206, 0.15) 90%,
                transparent 100%
            );
            transition: background 0.3s ease;
        }

        /* Responsive improvements pour la sidebar */
        @media (max-width: 1024px) {
            .sidebar-nav-item {
                padding: 0.75rem 0.875rem;
                font-size: 0.8rem;
            }

            .sidebar-nav-item:hover,
            .sidebar-nav-item.active {
                transform: none;
            }

            .sidebar-container::before,
            .sidebar-container::after {
                display: none;
            }

            .sidebar-container {
                box-shadow: none;
                border-right: 1px solid var(--color-border-light);
            }
        }

        /* Additional refinements for main content area */
        .main-content-area {
            position: relative;
            background: var(--color-background-secondary);
        }

        .main-content-area::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 2px;
            height: 100%;
            background: linear-gradient(
                180deg,
                transparent 0%,
                rgba(255, 255, 255, 0.8) 20%,
                rgba(255, 255, 255, 0.9) 50%,
                rgba(255, 255, 255, 0.8) 80%,
                transparent 100%
            );
            z-index: 10;
        }

        .progress-bar {
            height: 6px;
            border-radius: 3px;
            overflow: hidden;
            background-color: var(--color-background-tertiary);
        }

        .progress-value {
            height: 100%;
            border-radius: 3px;
            transition: width 0.3s ease;
        }

        .premium-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--color-border);
            border-radius: 8px;
            font-size: 0.875rem;
            background-color: var(--color-background);
            color: var(--color-text-primary);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .premium-input:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .premium-select {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid var(--color-border);
            border-radius: 8px;
            font-size: 0.875rem;
            background-color: var(--color-background);
            color: var(--color-text-primary);
            transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .premium-select:focus {
            outline: none;
            border-color: var(--color-accent);
            box-shadow: 0 0 0 3px rgba(49, 130, 206, 0.1);
        }

        .faq-question {
            cursor: pointer;
            padding: 1.5rem;
            border-bottom: 1px solid var(--color-border-light);
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .faq-question:hover {
            background-color: var(--color-background-secondary);
        }

        .faq-answer {
            padding: 1.5rem;
            background-color: var(--color-background-secondary);
            display: none;
            border-bottom: 1px solid var(--color-border-light);
        }

        .drag-drop-area {
            border: 2px dashed var(--color-border);
            border-radius: 12px;
            background-color: var(--color-background-secondary);
            padding: 3rem 2rem;
            text-align: center;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .drag-drop-area:hover, .drag-drop-area.dragover {
            border-color: var(--color-accent);
            background-color: rgba(49, 130, 206, 0.05);
            transform: translateY(-2px);
        }

        .premium-table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
        }

        .premium-table th {
            background-color: var(--color-background-secondary);
            padding: 1rem;
            text-align: left;
            font-weight: 600;
            font-size: 0.875rem;
            color: var(--color-text-secondary);
            border-bottom: 1px solid var(--color-border-light);
        }

        .premium-table th:first-child {
            border-top-left-radius: 8px;
        }

        .premium-table th:last-child {
            border-top-right-radius: 8px;
        }

        .premium-table td {
            padding: 1rem;
            border-bottom: 1px solid var(--color-border-light);
            font-size: 0.875rem;
        }

        .premium-table tr:hover {
            background-color: var(--color-background-secondary);
        }

        .section-header {
            margin-bottom: 2rem;
        }

        .section-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--color-text-primary);
            margin-bottom: 0.5rem;
            letter-spacing: -0.025em;
        }

        .section-subtitle {
            font-size: 1.125rem;
            color: var(--color-text-secondary);
            font-weight: 400;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--color-text-primary);
            margin-bottom: 0.5rem;
            letter-spacing: -0.01em;
        }

        .card-subtitle {
            font-size: 0.875rem;
            color: var(--color-text-secondary);
            margin-bottom: 1.5rem;
        }

        /* Simulator Scoped Styles */
        #simulator-container {
            /* Simulator CSS variables */
            --sim-primary: #2a5b8c;
            --sim-secondary: #4a90e2;
            --sim-accent: #ff6b35;
            --sim-success: #2e8b57;
            --sim-light: #f8f9fa;
            --sim-dark: #1d2d35;
            --sim-grey: #6c757d;
            --sim-border: #dee2e6;
            --sim-warning: #ffc107;
            --sim-font-main: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            --sim-font-heading: 'Montserrat', 'Arial Rounded MT Bold', sans-serif;
            --sim-space-xs: 0.5rem;
            --sim-space-sm: 1rem;
            --sim-space-md: 1.5rem;
            --sim-space-lg: 2rem;
            --sim-space-xl: 3rem;
            --sim-radius-sm: 4px;
            --sim-radius-md: 8px;
            --sim-radius-lg: 12px;
            --sim-radius-xl: 20px;
        }

        #simulator-container * {
            box-sizing: border-box;
        }

        /* Hero section */
        .simulator-hero {
            background: linear-gradient(135deg, rgba(42, 91, 140, 0.95), rgba(74, 144, 226, 0.85)), url('https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1951&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            text-align: center;
            border-radius: var(--sim-radius-xl);
            padding: var(--sim-space-xl);
            margin: var(--sim-space-md) 0;
        }

        .simulator-hero h1 {
            font-size: 2.5rem;
            margin-bottom: var(--sim-space-sm);
            font-family: var(--sim-font-heading);
        }

        .simulator-hero p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto var(--sim-space-lg);
            opacity: 0.9;
        }

        /* Features */
        .simulator-features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--sim-space-md);
            margin: var(--sim-space-xl) 0;
        }

        .simulator-feature {
            background: white;
            border-radius: var(--sim-radius-lg);
            padding: var(--sim-space-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.3s;
        }

        .simulator-feature:hover {
            transform: translateY(-5px);
        }

        .simulator-feature i {
            font-size: 2.5rem;
            color: var(--sim-primary);
            margin-bottom: var(--sim-space-sm);
        }

        .simulator-feature h3 {
            margin-bottom: var(--sim-space-xs);
            color: var(--sim-primary);
        }

        /* Warning */
        .simulator-warning {
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid var(--sim-warning);
            padding: var(--sim-space-md);
            border-radius: var(--sim-radius-sm);
            margin: var(--sim-space-lg) 0;
        }

        /* Button */
        .simulator-btn {
            display: inline-block;
            padding: 0.8rem 2rem;
            background-color: var(--sim-accent);
            color: white;
            border-radius: var(--sim-radius-md);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }

        .simulator-btn:hover {
            background-color: #e55a2b;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }

        .simulator-btn-secondary {
            background-color: var(--sim-secondary);
        }

        .simulator-btn-secondary:hover {
            background-color: #3a7fd9;
        }

        /* Form container */
        .simulator-form-container {
            background: white;
            border-radius: var(--sim-radius-xl);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin: var(--sim-space-lg) 0;
        }

        .simulator-form-header {
            padding: var(--sim-space-md);
            background: var(--sim-primary);
            color: white;
            text-align: center;
        }

        .simulator-progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin-top: var(--sim-space-sm);
            overflow: hidden;
        }

        .simulator-progress {
            height: 100%;
            background: white;
            width: 33%;
            border-radius: 4px;
            transition: width 0.5s;
        }

        .simulator-form-body {
            padding: var(--sim-space-lg);
        }

        .simulator-form-step {
            display: none;
        }

        .simulator-form-step.active {
            display: block;
        }

        .simulator-form-group {
            margin-bottom: var(--sim-space-lg);
        }

        .simulator-form-group h3 {
            margin-bottom: var(--sim-space-md);
            color: var(--sim-primary);
            border-bottom: 1px solid var(--sim-border);
            padding-bottom: var(--sim-space-xs);
        }

        .simulator-input-group {
            margin-bottom: var(--sim-space-md);
        }

        .simulator-input-group label {
            display: block;
            margin-bottom: var(--sim-space-xs);
            font-weight: 500;
        }

        #simulator-container input,
        #simulator-container select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid var(--sim-border);
            border-radius: var(--sim-radius-md);
            font-family: var(--sim-font-main);
            font-size: 1rem;
        }

        #simulator-container input:focus,
        #simulator-container select:focus {
            outline: none;
            border-color: var(--sim-secondary);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
        }

        .simulator-checkbox-group {
            display: flex;
            gap: var(--sim-space-sm);
            align-items: center;
            margin-bottom: var(--sim-space-xs);
        }

        .simulator-checkbox-group input {
            width: auto;
        }

        /* Info tooltip */
        .simulator-info-tooltip {
            display: inline-block;
            margin-left: var(--sim-space-xs);
            color: var(--sim-secondary);
            cursor: pointer;
            position: relative;
        }

        .simulator-tooltip-text {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--sim-dark);
            color: white;
            padding: var(--sim-space-sm);
            border-radius: var(--sim-radius-md);
            width: 250px;
            font-size: 0.9rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .simulator-info-tooltip:hover .simulator-tooltip-text {
            opacity: 1;
            visibility: visible;
        }

        /* Advanced options */
        .simulator-advanced-toggle {
            display: flex;
            align-items: center;
            gap: var(--sim-space-xs);
            color: var(--sim-primary);
            font-weight: 500;
            cursor: pointer;
            margin-top: var(--sim-space-sm);
        }

        .simulator-advanced-options {
            display: none;
            margin-top: var(--sim-space-md);
            padding-top: var(--sim-space-md);
            border-top: 1px dashed var(--sim-border);
        }

        /* Slider */
        .simulator-slider-container {
            margin-top: var(--sim-space-sm);
        }

        .simulator-slider {
            width: 100%;
            height: 8px;
            -webkit-appearance: none;
            appearance: none;
            background: var(--sim-border);
            outline: none;
            border-radius: 4px;
            margin: 1rem 0;
        }

        .simulator-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--sim-primary);
            cursor: pointer;
        }

        .simulator-slider-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: var(--sim-grey);
        }

        /* Form navigation */
        .simulator-form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: var(--sim-space-lg);
        }

        /* Results */
        .simulator-results-header {
            text-align: center;
            margin-bottom: var(--sim-space-xl);
        }

        .simulator-results-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--sim-space-lg);
            margin-bottom: var(--sim-space-xl);
        }

        .simulator-card {
            background: white;
            border-radius: var(--sim-radius-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: var(--sim-space-lg);
            text-align: center;
            position: relative;
            transition: all 0.3s;
        }

        .simulator-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        .simulator-card.simulator-recommended {
            border: 2px solid var(--sim-success);
            transform: scale(1.02);
        }

        .simulator-card.simulator-recommended::after {
            content: 'Recommandé';
            position: absolute;
            top: -12px;
            right: 20px;
            background: var(--sim-success);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: var(--sim-radius-md);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .simulator-card-header {
            padding-bottom: var(--sim-space-md);
            margin-bottom: var(--sim-space-md);
            border-bottom: 1px solid var(--sim-border);
        }

        .simulator-price {
            font-size: 2.5rem;
            font-weight: 700;
            margin: var(--sim-space-sm) 0;
            color: var(--sim-primary);
        }

        .simulator-price-period {
            font-size: 1rem;
            color: var(--sim-grey);
            font-weight: 400;
        }

        .simulator-details-toggle {
            color: var(--sim-secondary);
            font-weight: 500;
            cursor: pointer;
            margin: var(--sim-space-sm) 0;
            display: inline-block;
        }

        .simulator-details-content {
            display: none;
            text-align: left;
            margin-top: var(--sim-space-md);
            background: #f8fafc;
            padding: var(--sim-space-md);
            border-radius: var(--sim-radius-md);
        }

        /* Threshold graph */
        .simulator-threshold-graph {
            background: white;
            border-radius: var(--sim-radius-lg);
            padding: var(--sim-space-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: var(--sim-space-lg);
        }

        .simulator-graph-container {
            height: 100px;
            background: linear-gradient(to right, var(--sim-secondary), var(--sim-accent));
            border-radius: var(--sim-radius-md);
            margin: var(--sim-space-md) 0;
            position: relative;
        }

        .simulator-threshold-marker {
            position: absolute;
            top: -25px;
            width: 2px;
            height: 150px;
            background: var(--sim-dark);
        }

        .simulator-threshold-label {
            position: absolute;
            top: -45px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--sim-dark);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: var(--sim-radius-md);
            white-space: nowrap;
        }

        .simulator-current-position {
            position: absolute;
            bottom: -25px;
            transform: translateX(-50%);
            background: var(--sim-accent);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: var(--sim-radius-md);
            font-weight: 600;
        }

        .simulator-graph-labels {
            display: flex;
            justify-content: space-between;
            color: var(--sim-grey);
            font-size: 0.9rem;
        }

        /* Comparison table */
        .simulator-comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: var(--sim-space-lg) 0;
        }

        .simulator-comparison-table th,
        .simulator-comparison-table td {
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid var(--sim-border);
        }

        .simulator-comparison-table th {
            font-weight: 600;
            color: var(--sim-primary);
        }

        .simulator-comparison-table tr:last-child td {
            border-bottom: none;
        }

        /* Report */
        .simulator-report-container {
            text-align: center;
            padding: var(--sim-space-xl) 0;
        }

        .simulator-report-card {
            background: white;
            border-radius: var(--sim-radius-xl);
            padding: var(--sim-space-xl);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }

        /* FAQ */
        .simulator-faq-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .simulator-faq-item {
            margin-bottom: var(--sim-space-sm);
            border: 1px solid var(--sim-border);
            border-radius: var(--sim-radius-md);
            overflow: hidden;
        }

        .simulator-faq-question {
            padding: var(--sim-space-md);
            background: white;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .simulator-faq-answer {
            padding: 0 var(--sim-space-md);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s, padding 0.3s;
            background: #f8fafc;
        }

        .simulator-faq-item.active .simulator-faq-answer {
            padding: var(--sim-space-md);
            max-height: 500px;
        }

        /* Responsive */
        @media (max-width: 992px) {
            .simulator-features {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .simulator-hero {
                padding: var(--sim-space-lg);
            }

            .simulator-hero h1 {
                font-size: 2rem;
            }

            .simulator-features {
                grid-template-columns: 1fr;
            }

            .simulator-form-container {
                margin: var(--sim-space-md) 0;
            }

            .simulator-results-container {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) {
            .simulator-hero {
                margin: var(--sim-space-sm) 0;
                padding: var(--sim-space-lg) var(--sim-space-md);
            }

            .simulator-hero h1 {
                font-size: 1.8rem;
            }

            .simulator-btn {
                width: 100%;
            }

            .simulator-form-navigation {
                flex-direction: column;
                gap: var(--sim-space-sm);
            }
        }
    </style>
</head>
<body>
    <div class="flex h-screen bg-[var(--color-background-secondary)]">
        <!-- Barre de navigation latérale -->
        <aside class="sidebar-container w-72 flex-shrink-0 bg-[var(--color-background)] relative">
            <div class="flex h-full flex-col relative z-10">
                <div class="p-8 border-b border-[var(--color-border-light)] bg-gradient-to-r from-[var(--color-background)] to-[var(--color-background-secondary)]">
                    <h1 class="font-display text-2xl font-semibold text-[var(--color-primary)] mb-1 transition-colors duration-300">MyFrontaliers</h1>
                    <p class="text-sm text-[var(--color-text-muted)] font-medium">Cabinet Expert Comptable</p>
                    <div class="mt-4 h-1 w-16 bg-gradient-to-r from-[var(--color-accent)] to-[var(--color-secondary)] rounded-full"></div>
                </div>
                <nav class="flex-1 p-6 bg-[var(--color-background)]">
                    <div class="space-y-2">
                        <a class="sidebar-nav-item tab-button active" data-tab="cabinet">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                            </svg>
                            <span>Cabinet</span>
                        </a>
                        <a class="sidebar-nav-item tab-button" data-tab="fiscalite">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                            </svg>
                            <span>Fiscalité</span>
                        </a>
                        <a class="sidebar-nav-item tab-button" data-tab="social">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                            </svg>
                            <span>Social</span>
                        </a>
                        <a class="sidebar-nav-item tab-button" data-tab="depot">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                            </svg>
                            <span>Dépôt de Documents</span>
                        </a>
                        <a class="sidebar-nav-item tab-button" data-tab="actualites">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h10a2 2 0 012 2v1m2 13a2 2 0 01-2-2V7m2 13a2 2 0 002-2V9a2 2 0 00-2-2h-2m-4-3h2m-4 3h2M5 10h2a1 1 0 011 1v1a1 1 0 01-1 1H5a1 1 0 01-1-1v-1a1 1 0 011-1z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                            </svg>
                            <span>Actualités</span>
                        </a>
                        <a class="sidebar-nav-item tab-button" data-tab="faq">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.79 4 4 0 1.976-1.724 3.5-3.772 3.5-1.742 0-3.223-.835-3.772-2M12 18.5v.01M12 22C6.477 22 2 17.523 2 12S6.477 2 12 2s10 4.477 10 10-4.477 10-10 10z" stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"></path>
                            </svg>
                            <span>FAQ</span>
                        </a>
                    </div>
                </nav>
            </div>
        </aside>

        <!-- Contenu principal -->
        <main class="main-content-area flex-1 overflow-y-auto bg-[var(--color-background-secondary)]">
            <!-- Onglet Cabinet -->
            <div class="p-12 tab-content active" id="cabinet">
                <header class="section-header flex items-start justify-between">
                    <div>
                        <h1 class="section-title font-display">Tableau de Bord</h1>
                        <p class="section-subtitle">Vue d'ensemble de l'activité du cabinet</p>
                    </div>
                    <div class="flex items-center gap-3">
                        <button class="premium-button premium-button-primary">
                            <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path clip-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" fill-rule="evenodd"></path>
                            </svg>
                            Nouveau Client
                        </button>
                        <button class="premium-button premium-button-secondary">
                            <svg class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                <path d="M10.75 2.75a.75.75 0 00-1.5 0v8.5a.75.75 0 001.5 0v-8.5z"></path>
                                <path clip-rule="evenodd" d="M3.25 2a.75.75 0 000 1.5h13.5a.75.75 0 000-1.5H3.25zM2.5 13.5A.5.5 0 013 13h14a.5.5 0 01.5.5v1a.5.5 0 01-.5.5H3a.5.5 0 01-.5-.5v-1z" fill-rule="evenodd"></path>
                            </svg>
                            Lettre de Mission
                        </button>
                    </div>
                </header>
                <section class="premium-card p-8 mb-8">
                    <div class="mb-8 flex flex-wrap items-center justify-between gap-6">
                        <div>
                            <h2 class="card-title">Suivi des Dossiers</h2>
                            <p class="card-subtitle">Gestion et suivi des missions clients</p>
                        </div>
                        <div class="flex gap-3">
                            <select class="premium-select text-sm min-w-[140px]">
                                <option>Collaborateur</option>
                                <option>Marie Dubois</option>
                                <option>Pierre Martin</option>
                                <option>Sophie Leclerc</option>
                            </select>
                            <select class="premium-select text-sm min-w-[120px]">
                                <option>Canton</option>
                                <option>Genève</option>
                                <option>Vaud</option>
                            </select>
                            <select class="premium-select text-sm min-w-[120px]">
                                <option>Statut</option>
                                <option>À faire</option>
                                <option>En cours</option>
                                <option>En attente client</option>
                                <option>Terminé</option>
                            </select>
                        </div>
                    </div>
                    <div class="overflow-x-auto">
                        <table class="premium-table">
                            <thead>
                                <tr>
                                    <th>Client</th>
                                    <th>Mission</th>
                                    <th>Canton</th>
                                    <th>Assigné</th>
                                    <th>Statut</th>
                                    <th>Rentabilité</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="font-semibold text-[var(--color-text-primary)]">Client A</td>
                                    <td class="text-[var(--color-text-secondary)]">Déclaration Fiscale 2024</td>
                                    <td class="text-[var(--color-text-secondary)]">Genève</td>
                                    <td class="text-[var(--color-text-secondary)]">Marie Dubois</td>
                                    <td><span class="status-badge status-todo">À faire</span></td>
                                    <td class="text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-3">
                                            <div class="progress-bar w-20">
                                                <div class="progress-value bg-[var(--status-todo)]" style="width: 15%"></div>
                                            </div>
                                            <span class="text-sm font-medium">15%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-[var(--color-text-primary)]">Client B</td>
                                    <td class="text-[var(--color-text-secondary)]">Conseil Fiscal</td>
                                    <td class="text-[var(--color-text-secondary)]">Vaud</td>
                                    <td class="text-[var(--color-text-secondary)]">Pierre Martin</td>
                                    <td><span class="status-badge status-inprogress">En cours</span></td>
                                    <td class="text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-3">
                                            <div class="progress-bar w-20">
                                                <div class="progress-value bg-[var(--status-inprogress)]" style="width: 22%"></div>
                                            </div>
                                            <span class="text-sm font-medium">22%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-[var(--color-text-primary)]">Client C</td>
                                    <td class="text-[var(--color-text-secondary)]">Optimisation Fiscale</td>
                                    <td class="text-[var(--color-text-secondary)]">Vaud</td>
                                    <td class="text-[var(--color-text-secondary)]">Sophie Leclerc</td>
                                    <td><span class="status-badge status-waiting">En attente Client</span></td>
                                    <td class="text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-3">
                                            <div class="progress-bar w-20">
                                                <div class="progress-value bg-[var(--status-waiting)]" style="width: 10%"></div>
                                            </div>
                                            <span class="text-sm font-medium">10%</span>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="font-semibold text-[var(--color-text-primary)]">Client D</td>
                                    <td class="text-[var(--color-text-secondary)]">Déclaration Fiscale 2024</td>
                                    <td class="text-[var(--color-text-secondary)]">Genève</td>
                                    <td class="text-[var(--color-text-secondary)]">Marie Dubois</td>
                                    <td><span class="status-badge status-completed">Terminé</span></td>
                                    <td class="text-[var(--color-text-secondary)]">
                                        <div class="flex items-center gap-3">
                                            <div class="progress-bar w-20">
                                                <div class="progress-value bg-[var(--status-completed)]" style="width: 28%"></div>
                                            </div>
                                            <span class="text-sm font-medium">28%</span>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>

                <section class="premium-card p-8">
                    <div class="mb-6">
                        <h2 class="card-title">Formations à Venir</h2>
                        <p class="card-subtitle">Développement professionnel continu</p>
                    </div>
                    <div class="relative overflow-hidden rounded-xl bg-gradient-to-br from-[var(--color-primary)] via-[var(--color-secondary)] to-[var(--color-accent)] p-8 text-white">
                        <div class="relative z-10 flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium uppercase tracking-wider opacity-90 mb-2">Prochain module</p>
                                <p class="text-2xl font-semibold mb-1">Optimisation fiscale transfrontalière</p>
                                <p class="text-sm opacity-80">Formation certifiante - 16 heures</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium uppercase tracking-wider opacity-90 mb-2">Date limite</p>
                                <p class="text-2xl font-semibold">30/09/2025</p>
                            </div>
                        </div>
                        <div class="absolute top-0 right-0 w-32 h-32 bg-white opacity-5 rounded-full transform translate-x-16 -translate-y-16"></div>
                        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white opacity-5 rounded-full transform -translate-x-12 translate-y-12"></div>
                    </div>
                </section>
            </div>

            <!-- Onglet Fiscalité -->
            <div class="p-12 tab-content" id="fiscalite">
                <header class="section-header">
                    <h1 class="section-title font-display">Fiscalité</h1>
                    <p class="section-subtitle">Outils d'optimisation fiscale pour les travailleurs frontaliers</p>
                </header>

                <section class="premium-card p-8 mb-8">
                    <div class="mb-8">
                        <h2 class="card-title">Simulateur Quasi-Résident (Genève)</h2>
                        <p class="card-subtitle">Estimez l'économie potentielle avec le statut de quasi-résident</p>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:items-start">
                        <div class="bg-[var(--color-background-secondary)] rounded-xl p-8 h-full">
                            <h3 class="text-xl font-semibold mb-6 text-[var(--color-text-primary)]">Paramètres de simulation</h3>

                            <div class="space-y-6">
                                <div>
                                    <label class="block text-sm font-semibold text-[var(--color-text-primary)] mb-2">Revenus bruts en Suisse (CHF)</label>
                                    <input type="number" value="120000" class="premium-input text-lg">
                                </div>

                                <div>
                                    <label class="block text-sm font-semibold text-[var(--color-text-primary)] mb-2">Autres revenus (CHF)</label>
                                    <input type="number" value="15000" class="premium-input text-lg">
                                </div>

                                <div>
                                    <label class="block text-sm font-semibold text-[var(--color-text-primary)] mb-2">Situation familiale</label>
                                    <select class="premium-select text-lg">
                                        <option>Célibataire</option>
                                        <option selected>Marié(e)</option>
                                        <option>Divorcé(e)</option>
                                        <option>Veuf/Veuve</option>
                                    </select>
                                </div>

                                <div class="mt-8">
                                    <h4 class="text-sm font-semibold text-[var(--color-text-primary)] mb-4">Déductions possibles</h4>
                                    <div class="space-y-3">
                                        <label class="flex items-center gap-3 cursor-pointer">
                                            <input type="checkbox" checked class="rounded border-[var(--color-border)] text-[var(--color-accent)] focus:ring-[var(--color-accent)]">
                                            <span class="text-sm text-[var(--color-text-secondary)]">Frais de transport</span>
                                        </label>
                                        <label class="flex items-center gap-3 cursor-pointer">
                                            <input type="checkbox" class="rounded border-[var(--color-border)] text-[var(--color-accent)] focus:ring-[var(--color-accent)]">
                                            <span class="text-sm text-[var(--color-text-secondary)]">Frais de repas</span>
                                        </label>
                                        <label class="flex items-center gap-3 cursor-pointer">
                                            <input type="checkbox" checked class="rounded border-[var(--color-border)] text-[var(--color-accent)] focus:ring-[var(--color-accent)]">
                                            <span class="text-sm text-[var(--color-text-secondary)]">Frais de formation</span>
                                        </label>
                                    </div>
                                </div>

                                <div class="mt-8 pt-6 border-t border-[var(--color-border-light)]">
                                    <button class="premium-button premium-button-primary w-full text-lg">
                                        Calculer l'optimisation
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="bg-[var(--color-background-secondary)] rounded-xl p-8 h-full">
                            <h3 class="text-xl font-semibold mb-6 text-[var(--color-text-primary)]">Résultats de simulation</h3>

                            <div class="mb-8">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-[var(--color-text-secondary)]">Impôt sans optimisation</span>
                                    <span class="font-semibold text-lg text-[var(--color-text-primary)]">32 450 CHF</span>
                                </div>
                                <div class="h-2 w-full bg-[var(--color-background-tertiary)] rounded-full overflow-hidden">
                                    <div class="h-full bg-[var(--color-accent)]" style="width: 100%"></div>
                                </div>
                            </div>

                            <div class="mb-8">
                                <div class="flex justify-between items-center mb-3">
                                    <span class="text-[var(--color-text-secondary)]">Impôt avec statut Quasi-Résident</span>
                                    <span class="font-semibold text-lg text-[var(--color-text-primary)]">19 750 CHF</span>
                                </div>
                                <div class="h-2 w-full bg-[var(--color-background-tertiary)] rounded-full overflow-hidden">
                                    <div class="h-full bg-[var(--color-success)]" style="width: 61%"></div>
                                </div>
                            </div>

                            <div class="bg-gradient-to-r from-[var(--color-success)] to-[var(--color-success-light)] text-white rounded-xl p-6 mb-8">
                                <div class="text-center">
                                    <p class="text-sm font-medium opacity-90 mb-1">Économie potentielle</p>
                                    <p class="text-3xl font-bold">12 700 CHF</p>
                                    <p class="text-sm opacity-80 mt-1">par année</p>
                                </div>
                            </div>

                            <div class="mb-6">
                                <h4 class="font-semibold mb-4 text-[var(--color-text-primary)]">Déductions applicables</h4>
                                <ul class="space-y-3">
                                    <li class="flex items-center gap-3">
                                        <div class="w-5 h-5 rounded-full bg-[var(--color-success)] flex items-center justify-center">
                                            <svg class="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                        <span class="text-[var(--color-text-secondary)]">Frais de transport (3 500 CHF)</span>
                                    </li>
                                    <li class="flex items-center gap-3">
                                        <div class="w-5 h-5 rounded-full bg-[var(--color-success)] flex items-center justify-center">
                                            <svg class="h-3 w-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                            </svg>
                                        </div>
                                        <span class="text-[var(--color-text-secondary)]">Frais de formation (1 200 CHF)</span>
                                    </li>
                                </ul>
                            </div>

                            <button class="premium-button premium-button-accent w-full">
                                Générer le rapport détaillé
                            </button>
                        </div>
                    </div>
                </section>
                
                <section class="premium-card p-8 mb-8">
                    <div class="mb-8">
                        <h2 class="card-title">Arbre de décision : Quel statut fiscal pour mon client ?</h2>
                        <p class="card-subtitle">Outil interactif pour déterminer le statut fiscal optimal</p>
                    </div>

                    <div class="bg-[var(--color-background-secondary)] rounded-xl p-8">
                        <div class="max-w-3xl mx-auto">
                            <div class="flex flex-col items-center mb-8">
                                <div class="bg-[var(--color-primary)] text-white rounded-full w-16 h-16 flex items-center justify-center mb-2">
                                    1
                                </div>
                                <h3 class="text-lg font-semibold mb-4 text-[var(--color-text-primary)]">Client domicilié en France ?</h3>
                                <div class="flex gap-4">
                                    <button class="premium-button premium-button-primary">Oui</button>
                                    <button class="premium-button premium-button-secondary">Non</button>
                                </div>
                            </div>
                            
                            <div class="flex justify-around">
                                <div class="flex flex-col items-center">
                                    <div class="bg-[var(--color-primary)] text-white rounded-full w-16 h-16 flex items-center justify-center mb-2">
                                        2
                                    </div>
                                    <h3 class="text-lg font-semibold mb-4 text-[var(--color-text-primary)]">Résidence principale en France ?</h3>
                                    <div class="flex gap-4">
                                        <button class="premium-button premium-button-primary">Oui</button>
                                        <button class="premium-button premium-button-secondary">Non</button>
                                    </div>
                                </div>
                                
                                <div class="flex flex-col items-center">
                                    <div class="bg-[var(--color-primary)] text-white rounded-full w-16 h-16 flex items-center justify-center mb-2">
                                        3
                                    </div>
                                    <h3 class="text-lg font-semibold mb-4 text-[var(--color-text-primary)]">Activité ≥50% en Suisse ?</h3>
                                    <div class="flex gap-4">
                                        <button class="premium-button premium-button-primary">Oui</button>
                                        <button class="premium-button premium-button-secondary">Non</button>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex justify-around mt-8 gap-6">
                                <div class="text-center p-6 bg-[var(--color-success-light)] rounded-xl flex-1">
                                    <h4 class="font-semibold text-lg mb-2 text-[var(--color-text-primary)]">Statut frontalier standard</h4>
                                    <p class="text-[var(--color-text-secondary)]">Impôt à la source en Suisse</p>
                                </div>

                                <div class="text-center p-6 bg-[var(--color-warning)] text-white rounded-xl flex-1">
                                    <h4 class="font-semibold text-lg mb-2">Statut Quasi-Résident</h4>
                                    <p class="opacity-90">Imposition en France</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Onglet Social -->
            <div class="p-12 tab-content" id="social">
                <header class="section-header">
                    <h1 class="section-title font-display">Social</h1>
                    <p class="section-subtitle">Comparateurs et outils pour la protection sociale</p>
                </header>

                <!-- Simulateur LAMal vs CMU intégré -->
                <div id="simulator-container">
                    <!-- Page d'accueil du simulateur -->
                    <section id="simulator-home">
                        <div class="simulator-hero">
                            <h1>Simulateur LAMal vs. CMU pour frontaliers</h1>
                            <p>Obtenez une comparaison personnalisée, fiable et 100% gratuite basée sur les chiffres officiels 2025</p>
                            <a href="#simulator-form" class="simulator-btn" onclick="showSimulatorForm()">Démarrer ma simulation</a>
                        </div>

                        <div class="simulator-features">
                            <div class="simulator-feature">
                                <i class="fas fa-calculator"></i>
                                <h3>Calculs Précis</h3>
                                <p>Basés sur les données officielles 2025 pour des résultats fiables</p>
                            </div>
                            <div class="simulator-feature">
                                <i class="fas fa-bolt"></i>
                                <h3>Résultats Instantanés</h3>
                                <p>Comparaison immédiate avec visualisation claire des données</p>
                            </div>
                            <div class="simulator-feature">
                                <i class="fas fa-file-pdf"></i>
                                <h3>Rapport Détaillé</h3>
                                <p>Téléchargez votre analyse personnalisée pour prendre votre décision</p>
                            </div>
                        </div>

                        <div class="simulator-warning">
                            <p><strong>Important :</strong> Votre choix d'assurance maladie est obligatoire et irrévocable pendant toute l'année civile. Cette décision impacte votre protection santé et votre budget. Prenez le temps de bien comparer les options.</p>
                        </div>
                    </section>

                    <!-- Simulateur -->
                    <section id="simulator-form" style="display: none;">
                        <h2 style="text-align: center; margin-bottom: var(--space-lg);">Simulez votre assurance maladie</h2>

                        <div class="simulator-form-container">
                            <div class="simulator-form-header">
                                <h3>Votre situation personnelle</h3>
                                <div class="simulator-progress-bar">
                                    <div class="simulator-progress" id="formProgress"></div>
                                </div>
                            </div>

                            <div class="simulator-form-body">
                                <!-- Étape 1: Votre Situation -->
                                <div class="simulator-form-step active" id="step1">
                                    <div class="simulator-form-group">
                                        <h3>1. Votre situation familiale</h3>
                                        <div class="simulator-input-group">
                                            <label>Situation familiale</label>
                                            <select id="familySituation">
                                                <option value="single">Célibataire</option>
                                                <option value="married">Marié/Pacsé</option>
                                                <option value="divorced">Divorcé/Séparé</option>
                                                <option value="widowed">Veuf/Veuve</option>
                                            </select>
                                        </div>

                                        <div class="simulator-input-group">
                                            <label>Nombre d'enfants à charge</label>
                                            <input type="number" id="childrenCount" min="0" max="10" value="0">
                                        </div>

                                        <div class="simulator-input-group" id="childrenAges" style="display: none;">
                                            <label>Âges des enfants (séparés par des virgules)</label>
                                            <input type="text" placeholder="Ex: 5, 8, 12">
                                        </div>

                                        <div class="simulator-input-group">
                                            <label>Votre conjoint(e) perçoit-il/elle des revenus en France ?</label>
                                            <div style="display: flex; gap: var(--space-md); margin-top: var(--space-xs);">
                                                <div class="simulator-checkbox-group">
                                                    <input type="radio" id="spouseIncomeYes" name="spouseIncome" value="yes">
                                                    <label for="spouseIncomeYes">Oui</label>
                                                </div>
                                                <div class="simulator-checkbox-group">
                                                    <input type="radio" id="spouseIncomeNo" name="spouseIncome" value="no" checked>
                                                    <label for="spouseIncomeNo">Non</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="simulator-form-group">
                                        <h3>2. Votre activité professionnelle</h3>
                                        <div class="simulator-input-group">
                                            <label>Canton de travail en Suisse</label>
                                            <select id="canton">
                                                <option value="geneve">Genève</option>
                                                <option value="vaud">Vaud</option>
                                                <option value="neuchatel">Neuchâtel</option>
                                                <option value="jura">Jura</option>
                                                <option value="valais">Valais</option>
                                                <option value="basel">Bâle</option>
                                            </select>
                                        </div>

                                        <div class="simulator-input-group">
                                            <label>Votre âge</label>
                                            <input type="number" id="age" min="18" max="70" value="35">
                                        </div>
                                    </div>

                                    <div class="simulator-form-navigation">
                                        <div></div> <!-- Spacer -->
                                        <button class="simulator-btn" onclick="nextStep(2)">Suivant</button>
                                    </div>
                                </div>

                                <!-- Étape 2: Vos Revenus -->
                                <div class="simulator-form-step" id="step2">
                                    <div class="simulator-form-group">
                                        <h3>3. Vos revenus</h3>
                                        <div class="simulator-input-group">
                                            <label>Salaire Brut Annuel en CHF</label>
                                            <input type="number" id="salary" value="80000">
                                        </div>

                                        <div class="simulator-input-group">
                                            <label>
                                                Revenu Fiscal de Référence (RFR) de l'année N-2 (2023 pour la cotisation 2025)
                                                <span class="simulator-info-tooltip">
                                                    <i class="fas fa-info-circle"></i>
                                                    <span class="simulator-tooltip-text">Trouvez cette information sur votre avis d'imposition français, en haut de la première page.</span>
                                                </span>
                                            </label>
                                            <input type="number" id="rfr" value="35000">
                                        </div>

                                        <div class="simulator-advanced-toggle" onclick="toggleAdvanced()">
                                            <i class="fas fa-cog"></i>
                                            <span>Options de simulation avancée</span>
                                        </div>

                                        <div class="simulator-advanced-options" id="advancedOptions">
                                            <div class="simulator-input-group">
                                                <label>Déductions spécifiques (rachat 2ème pilier, frais réels, etc.)</label>
                                                <input type="number" id="deductions" value="0">
                                            </div>

                                            <div class="simulator-input-group">
                                                <label>
                                                    Taux de change CHF/EUR
                                                    <span class="simulator-info-tooltip">
                                                        <i class="fas fa-info-circle"></i>
                                                        <span class="simulator-tooltip-text">Taux officiel Banque de France. Modifiable si vous souhaitez simuler avec un taux différent.</span>
                                                    </span>
                                                </label>
                                                <input type="number" id="exchangeRate" step="0.01" value="1.07">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="simulator-form-navigation">
                                        <button class="simulator-btn simulator-btn-secondary" onclick="prevStep(1)">Précédent</button>
                                        <button class="simulator-btn" onclick="nextStep(3)">Suivant</button>
                                    </div>
                                </div>

                                <!-- Étape 3: Vos Besoins de Santé -->
                                <div class="simulator-form-step" id="step3">
                                    <div class="simulator-form-group">
                                        <h3>4. Vos besoins de santé</h3>
                                        <div class="simulator-input-group">
                                            <label>Où prévoyez-vous de vous faire soigner principalement ?</label>
                                            <select id="healthcareLocation">
                                                <option value="france">France</option>
                                                <option value="switzerland">Suisse</option>
                                                <option value="both">Les deux indifféremment</option>
                                            </select>
                                        </div>

                                        <div class="simulator-input-group">
                                            <label>Pour affiner la simulation LAMal, estimez vos dépenses de santé annuelles (consultations, pharmacie...)</label>
                                            <div class="simulator-slider-container">
                                                <input type="range" min="0" max="2" value="1" class="simulator-slider" id="healthCosts">
                                                <div class="simulator-slider-labels">
                                                    <span>Faibles (&lt; 300 CHF)</span>
                                                    <span>Moyennes (~1000 CHF)</span>
                                                    <span>Élevées (&gt; 2000 CHF)</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="simulator-form-navigation">
                                        <button class="simulator-btn simulator-btn-secondary" onclick="prevStep(2)">Précédent</button>
                                        <button class="simulator-btn" onclick="calculateResults()">Voir les résultats</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- Résultats -->
                    <section id="simulator-results" style="display: none;">
                        <div class="simulator-results-header">
                            <h2>Résultats de votre simulation</h2>
                            <p>Comparaison personnalisée basée sur votre situation</p>
                        </div>

                        <div class="simulator-results-container">
                            <div class="simulator-card" id="lamalCard">
                                <div class="simulator-card-header">
                                    <h3>LAMal</h3>
                                    <p>Assurance maladie suisse</p>
                                </div>
                                <div class="simulator-price">124€ <span class="simulator-price-period">/ mois</span></div>
                                <p>Coût annuel estimé: <strong>1,488€</strong></p>
                                <p class="simulator-details-toggle" onclick="toggleDetails('lamalDetails')">Voir le détail du calcul</p>
                                <div class="simulator-details-content" id="lamalDetails">
                                    <p><strong>Détail du calcul:</strong></p>
                                    <ul>
                                        <li>Prime de base: 95€/mois</li>
                                        <li>Franchise: 300€</li>
                                        <li>Quote-part: 93€</li>
                                        <li>Total annuel: 1,488€</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="simulator-card simulator-recommended" id="cmuCard">
                                <div class="simulator-card-header">
                                    <h3>CMU</h3>
                                    <p>Couverture Maladie Universelle française</p>
                                </div>
                                <div class="simulator-price">87€ <span class="simulator-price-period">/ mois</span></div>
                                <p>Coût annuel estimé: <strong>1,044€</strong></p>
                                <p class="simulator-details-toggle" onclick="toggleDetails('cmuDetails')">Voir le détail du calcul</p>
                                <div class="simulator-details-content" id="cmuDetails">
                                    <p><strong>Détail du calcul:</strong></p>
                                    <ul>
                                        <li>RFR: 35,000€</li>
                                        <li>Abattement: 5,000€</li>
                                        <li>Taux: 8%</li>
                                        <li>Total annuel: (35,000 - 5,000) × 8% = 1,044€</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="simulator-threshold-graph">
                            <h3>À partir de quel revenu la LAMal devient-elle plus intéressante ?</h3>
                            <p>Pour votre situation familiale, le seuil de basculement est de <strong>42,000€ de RFR</strong></p>

                            <div class="simulator-graph-container">
                                <div class="simulator-threshold-marker" style="left: 65%;"></div>
                                <div class="simulator-threshold-label">Seuil: 42,000€</div>
                                <div class="simulator-current-position" style="left: 45%;">Votre RFR: 35,000€</div>
                            </div>

                            <div class="simulator-graph-labels">
                                <span>CMU avantageuse</span>
                                <span>LAMal avantageuse</span>
                            </div>
                        </div>

                        <div class="simulator-comparison-table">
                            <h3 style="text-align: center; margin-bottom: var(--space-md);">Comparaison des avantages et inconvénients</h3>
                            <table>
                                <thead>
                                    <tr>
                                        <th>Critère</th>
                                        <th>LAMal</th>
                                        <th>CMU</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>Couverture des soins en Suisse</td>
                                        <td><i class="fas fa-check" style="color: var(--success);"></i> Excellente</td>
                                        <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Limitée</td>
                                    </tr>
                                    <tr>
                                        <td>Couverture des soins en France</td>
                                        <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Sous conditions</td>
                                        <td><i class="fas fa-check" style="color: var(--success);"></i> Complète</td>
                                    </tr>
                                    <tr>
                                        <td>Couverture de la famille</td>
                                        <td><i class="fas fa-times" style="color: #e53e3e;"></i> Payante par personne</td>
                                        <td><i class="fas fa-check" style="color: var(--success);"></i> Incluse pour ayants droit</td>
                                    </tr>
                                    <tr>
                                        <td>Démarches administratives</td>
                                        <td><i class="fas fa-check" style="color: var(--success);"></i> Simplifiées en Suisse</td>
                                        <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Complexes pour remboursements</td>
                                    </tr>
                                    <tr>
                                        <td>Impact des hauts revenus</td>
                                        <td><i class="fas fa-check" style="color: var(--success);"></i> Prime fixe, indépendante du revenu</td>
                                        <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Contribution proportionnelle au RFR</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>

                        <div class="simulator-card" style="background: #f0f7ff; border-left: 4px solid var(--primary); margin-top: var(--space-xl);">
                            <h3>Recommandation personnalisée</h3>
                            <p>Selon votre profil (famille avec conjoint sans revenus en France), <strong>la CMU présente un avantage financier notable</strong> avec une économie annuelle d'environ 444€.</p>
                            <p>Pour vos besoins de santé principalement en France, la CMU offre également une meilleure couverture et des démarches simplifiées.</p>
                        </div>

                        <div style="text-align: center; margin-top: var(--space-xl);">
                            <button class="simulator-btn" onclick="showReport()">Télécharger mon rapport personnalisé (PDF)</button>
                        </div>
                    </section>

                    <!-- Rapport -->
                    <section id="simulator-report" style="display: none;">
                        <div class="simulator-report-container">
                            <h2 style="margin-bottom: var(--space-md);">Votre rapport personnalisé est prêt !</h2>
                            <p>Téléchargez votre analyse complète pour prendre une décision éclairée</p>

                            <div class="simulator-report-card">
                                <i class="fas fa-file-pdf" style="font-size: 4rem; color: var(--accent); margin-bottom: var(--space-md);"></i>
                                <h3 style="margin-bottom: var(--space-sm);">Rapport d'analyse LAMal vs. CMU</h3>
                                <p style="margin-bottom: var(--space-lg);">Personnalisé pour <strong>Martin Dupont</strong> - Généré le 15/07/2025</p>

                                <a href="#" class="simulator-btn" style="margin-bottom: var(--space-xl);">
                                    <i class="fas fa-download"></i> Télécharger le rapport (PDF)
                                </a>

                                <div class="simulator-warning">
                                    <p><strong>Avertissement :</strong> Cette simulation a une valeur informative et ne constitue pas un conseil personnalisé. Les résultats sont basés sur les informations fournies et les données officielles disponibles en juillet 2025. Pour une analyse précise de votre situation, consultez un conseiller spécialisé.</p>
                                </div>
                            </div>
                        </div>
                    </section>

                    <!-- FAQ -->
                    <section id="simulator-faq" style="background: #f8fafc; padding: var(--space-xl) 0;">
                        <h2 style="text-align: center; margin-bottom: var(--space-xl);">Foire Aux Questions</h2>

                        <div class="simulator-faq-container">
                            <div class="simulator-faq-item">
                                <div class="simulator-faq-question" onclick="toggleFAQ(this)">
                                    <span>Quelle est la différence entre la LAMal et la CMU ?</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="simulator-faq-answer">
                                    <p>La LAMal (Loi sur l'Assurance Maladie) est le système d'assurance maladie obligatoire en Suisse. La CMU (Couverture Maladie Universelle) est le système français. En tant que frontalier, vous pouvez choisir entre les deux systèmes, mais ce choix est annuel et irrévocable.</p>
                                </div>
                            </div>

                            <div class="simulator-faq-item">
                                <div class="simulator-faq-question" onclick="toggleFAQ(this)">
                                    <span>Où trouver mon Revenu Fiscal de Référence (RFR) ?</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="simulator-faq-answer">
                                    <p>Votre RFR figure sur votre avis d'imposition français, en haut de la première page. Il correspond à votre revenu net imposable après abattements. Pour la cotisation 2025, on utilise le RFR de l'année 2023 (N-2).</p>
                                </div>
                            </div>

                            <div class="simulator-faq-item">
                                <div class="simulator-faq-question" onclick="toggleFAQ(this)">
                                    <span>Puis-je changer d'option en cours d'année ?</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="simulator-faq-answer">
                                    <p>Non, votre choix est annuel et irrévocable. Vous devez faire votre déclaration avant le 30 novembre pour l'année suivante. Les seules exceptions concernent des changements majeurs de situation (mariage, naissance, perte d'emploi).</p>
                                </div>
                            </div>

                            <div class="simulator-faq-item">
                                <div class="simulator-faq-question" onclick="toggleFAQ(this)">
                                    <span>Comment est calculée la contribution CMU ?</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="simulator-faq-answer">
                                    <p>La contribution CMU est calculée comme suit : (Revenu Fiscal de Référence - Abattement) × Taux (8% en 2025). L'abattement est de 8,922€ pour une personne seule et augmente selon votre situation familiale.</p>
                                </div>
                            </div>

                            <div class="simulator-faq-item">
                                <div class="simulator-faq-question" onclick="toggleFAQ(this)">
                                    <span>Quelle option est préférable pour une famille ?</span>
                                    <i class="fas fa-chevron-down"></i>
                                </div>
                                <div class="simulator-faq-answer">
                                    <p>La CMU présente souvent un avantage financier pour les familles car elle couvre gratuitement les enfants et le conjoint sans revenus. Avec la LAMal, chaque membre de la famille doit souscrire une assurance séparée, ce qui augmente significativement le coût.</p>
                                </div>
                            </div>
                        </div>
                    </section>
                </div>
            </div>

            <!-- Onglet Dépôt de Documents -->
            <div class="p-12 tab-content" id="depot">
                <header class="section-header">
                    <div>
                        <h1 class="section-title font-display">Dépôt de Documents</h1>
                        <div class="flex items-center gap-3 mt-3">
                            <div class="flex items-center gap-2 px-3 py-1 bg-[var(--color-success-light)] text-[var(--color-success)] rounded-full text-sm font-medium">
                                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                </svg>
                                <span>Connexion sécurisée SSL</span>
                            </div>
                        </div>
                    </div>
                </header>

                <section class="premium-card p-8 mb-8">
                    <div class="mb-8">
                        <h2 class="card-title">Déposer un document</h2>
                        <p class="card-subtitle">Sélectionnez un client pour déposer des documents</p>
                    </div>
                    
                    <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                        <div class="bg-[var(--color-background-secondary)] rounded-xl p-6">
                            <h3 class="font-semibold mb-6 text-[var(--color-text-primary)]">Sélection du client</h3>

                            <div class="mb-6">
                                <label class="block text-sm font-semibold text-[var(--color-text-primary)] mb-2">Client</label>
                                <select class="premium-select">
                                    <option>Sélectionnez un client...</option>
                                    <option selected>Martin Dupont</option>
                                    <option>Sophie Bernard</option>
                                    <option>Thomas Moreau</option>
                                    <option>Camille Lefevre</option>
                                </select>
                            </div>

                            <div class="text-sm">
                                <p class="font-semibold text-[var(--color-text-primary)] mb-3">Informations client</p>
                                <ul class="space-y-2 text-[var(--color-text-secondary)]">
                                    <li class="flex justify-between">
                                        <span>ID Client:</span>
                                        <span class="font-medium">MC-4582</span>
                                    </li>
                                    <li class="flex justify-between">
                                        <span>Canton:</span>
                                        <span class="font-medium">Genève</span>
                                    </li>
                                    <li class="flex justify-between">
                                        <span>Dernier dépôt:</span>
                                        <span class="font-medium">12/06/2025</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                        
                        <div class="lg:col-span-2">
                            <div class="drag-drop-area">
                                <div class="mb-4">
                                    <svg class="h-12 w-12 mx-auto text-[var(--color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl font-bold mb-2">Glissez vos documents ici</h3>
                                <p class="text-[var(--color-text-secondary)] mb-4">Formats acceptés : PDF, JPG, PNG (max. 10 Mo par fichier)</p>
                                <button class="rounded-lg bg-white border border-[var(--color-border)] px-4 py-2 text-[var(--color-text-secondary)] hover:bg-gray-50">
                                    Ou parcourir vos fichiers
                                </button>
                            </div>
                            
                            <div class="mt-6">
                                <h3 class="font-bold mb-3">Documents en cours de dépôt</h3>
                                <div class="border border-[var(--color-border)] rounded-lg">
                                    <div class="p-3 border-b flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <svg class="h-5 w-5 text-[var(--color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span>Déclaration_impots_2024.pdf</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="text-sm text-[var(--color-text-secondary)]">12.4 Mo</span>
                                            <button class="text-red-500">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="p-3 flex items-center justify-between">
                                        <div class="flex items-center gap-2">
                                            <svg class="h-5 w-5 text-[var(--color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <span>Relevé_bancaire_Q2_2025.pdf</span>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <span class="text-sm text-[var(--color-text-secondary)]">8.2 Mo</span>
                                            <button class="text-red-500">
                                                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="mt-6 flex justify-end">
                                <button class="rounded-lg bg-[var(--color-secondary)] px-4 py-2 text-white hover:bg-[var(--color-primary)] transition-colors">
                                    Envoyer les documents
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
                
                <section class="rounded-lg bg-white p-6 shadow-sm">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Documents récents</h2>
                    
                    <div class="overflow-x-auto">
                        <table class="w-full text-left">
                            <thead>
                                <tr class="border-b border-[var(--color-border)] bg-gray-50">
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Document</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Client</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Date de dépôt</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Statut</th>
                                    <th class="px-4 py-3 text-sm font-medium text-[var(--color-text-secondary)]">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-[var(--color-border)]">
                                <tr>
                                    <td class="px-4 py-3 font-medium">Avis d'imposition 2024</td>
                                    <td class="px-4 py-3">Martin Dupont</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">15/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-completed">Traité</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Télécharger</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Contrat de travail</td>
                                    <td class="px-4 py-3">Sophie Bernard</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">14/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-inprogress">En vérification</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Voir</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Relevé bancaire Q2</td>
                                    <td class="px-4 py-3">Thomas Moreau</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">10/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-completed">Traité</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Télécharger</button>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="px-4 py-3 font-medium">Justificatif domicile</td>
                                    <td class="px-4 py-3">Camille Lefevre</td>
                                    <td class="px-4 py-3 text-[var(--color-text-secondary)]">08/07/2025</td>
                                    <td class="px-4 py-3"><span class="status-badge status-waiting">En attente</span></td>
                                    <td class="px-4 py-3">
                                        <button class="text-[var(--color-secondary)] hover:underline">Voir</button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </section>
            </div>

            <!-- Onglet Actualités -->
            <div class="p-12 tab-content" id="actualites">
                <header class="section-header">
                    <h1 class="section-title font-display">Actualités</h1>
                    <p class="section-subtitle">Les dernières informations sur la fiscalité transfrontalière</p>
                </header>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <article class="premium-card overflow-hidden group hover:shadow-lg transition-all duration-300">
                        <div class="h-48 bg-gradient-to-br from-[var(--color-accent)] to-[var(--color-secondary)] relative overflow-hidden">
                            <div class="absolute inset-0 bg-black bg-opacity-20"></div>
                            <div class="absolute bottom-4 left-4 right-4">
                                <span class="inline-block px-3 py-1 bg-white bg-opacity-90 text-[var(--color-primary)] text-xs font-semibold rounded-full">Fiscalité</span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-4">
                                <time class="text-sm text-[var(--color-text-muted)] font-medium">10 juillet 2025</time>
                            </div>
                            <h3 class="text-xl font-semibold mb-3 text-[var(--color-text-primary)] group-hover:text-[var(--color-accent)] transition-colors">Nouveaux seuils pour le télétravail des frontaliers à Genève</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4 leading-relaxed">Les autorités genevoises ont annoncé une augmentation du nombre de jours de télétravail autorisés pour les frontaliers, passant de 34 à 45 jours par an à compter du 1er janvier 2026.</p>
                            <button class="inline-flex items-center text-[var(--color-accent)] font-semibold hover:text-[var(--color-primary)] transition-colors">
                                Lire la suite
                                <svg class="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                                </svg>
                            </button>
                        </div>
                    </article>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1554224155-6726b3ff858f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-success)] text-white text-xs rounded">Social</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">08/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Réforme des cotisations sociales pour les travailleurs frontaliers</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Une nouvelle convention entre la France et la Suisse modifie les règles de cotisation sociale pour les frontaliers. Découvrez les impacts sur votre protection sociale.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-cta)] text-white text-xs rounded">Échéance</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">05/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Déclaration fiscale 2025 : dates importantes à retenir</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Le calendrier des déclarations fiscales pour 2025 vient d'être publié. Notez dès maintenant les dates limites pour les résidents et frontaliers.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1553877522-43269d4ea984?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-secondary)] text-white text-xs rounded">Fiscalité</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">02/07/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Optimisation fiscale : les nouvelles stratégies pour 2025</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Découvrez les dernières stratégies d'optimisation fiscale pour les travailleurs frontaliers suite aux changements législatifs en France et en Suisse.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1589156280159-27698a70f29e?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-success)] text-white text-xs rounded">Retraite</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">28/06/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Réforme des retraites : impacts pour les frontaliers</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">La réforme des retraites en France aura des conséquences spécifiques pour les travailleurs frontaliers. Analyse des changements et stratégies d'adaptation.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                    
                    <div class="rounded-lg bg-white shadow-sm overflow-hidden">
                        <div class="h-48 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1551836022-d5d88e9218df?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80');"></div>
                        <div class="p-6">
                            <div class="flex justify-between items-start mb-3">
                                <span class="px-2 py-1 bg-[var(--color-cta)] text-white text-xs rounded">Formation</span>
                                <span class="text-sm text-[var(--color-text-secondary)]">25/06/2025</span>
                            </div>
                            <h3 class="text-xl font-bold mb-2">Nouveau module de formation : optimisation transfrontalière</h3>
                            <p class="text-[var(--color-text-secondary)] mb-4">Notre nouveau module de formation sur les dernières techniques d'optimisation fiscale transfrontalière est désormais disponible pour tous les collaborateurs.</p>
                            <button class="text-[var(--color-secondary)] font-medium hover:underline">Lire la suite</button>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8 text-center">
                    <button class="rounded-lg border border-[var(--color-border)] bg-white px-4 py-2 text-[var(--color-text-secondary)] shadow-sm transition-colors hover:bg-gray-50">
                        Charger plus d'actualités
                    </button>
                </div>
            </div>

            <!-- Onglet FAQ -->
            <div class="p-12 tab-content" id="faq">
                <header class="section-header">
                    <h1 class="section-title font-display">Foire Aux Questions</h1>
                    <p class="section-subtitle">Trouvez des réponses aux questions fréquentes sur la fiscalité transfrontalière</p>
                </header>

                <div class="premium-card overflow-hidden">
                    <div class="border-b border-[var(--color-border-light)] last:border-b-0">
                        <div class="faq-question p-6 cursor-pointer hover:bg-[var(--color-background-secondary)] transition-colors" data-category="fiscalite">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-[var(--color-text-primary)] pr-4">Quelles sont les conditions pour bénéficier du statut de quasi-résident à Genève ?</span>
                                <svg class="h-5 w-5 transform transition-transform text-[var(--color-text-muted)] flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer px-6 pb-6 text-[var(--color-text-secondary)] leading-relaxed">
                            <p class="mb-4">Pour bénéficier du statut de quasi-résident à Genève, vous devez remplir les conditions suivantes :</p>
                            <ul class="space-y-3">
                                <li class="flex items-start gap-3">
                                    <span class="w-1.5 h-1.5 bg-[var(--color-accent)] rounded-full mt-2 flex-shrink-0"></span>
                                    <span>Être domicilié fiscalement en France</span>
                                </li>
                                <li class="flex items-start gap-3">
                                    <span class="w-1.5 h-1.5 bg-[var(--color-accent)] rounded-full mt-2 flex-shrink-0"></span>
                                    <span>Travailler principalement dans le canton de Genève (au moins 90% de votre activité professionnelle)</span>
                                </li>
                                <li class="flex items-start gap-3">
                                    <span class="w-1.5 h-1.5 bg-[var(--color-accent)] rounded-full mt-2 flex-shrink-0"></span>
                                    <span>Ne pas avoir eu votre résidence principale en Suisse durant les 5 dernières années</span>
                                </li>
                                <li class="flex items-start gap-3">
                                    <span class="w-1.5 h-1.5 bg-[var(--color-accent)] rounded-full mt-2 flex-shrink-0"></span>
                                    <span>Disposer d'une résidence permanente en France</span>
                                </li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="fiscalite">
                            <div class="flex justify-between items-center">
                                <span>Comment déclarer mes revenus suisses en France ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Les revenus suisses doivent être déclarés en France selon les modalités suivantes :</p>
                            <ol class="list-decimal pl-5 mt-2 space-y-1">
                                <li>Déclarez vos revenus bruts suisses dans la catégorie "Traitements et salaires"</li>
                                <li>Appliquez un abattement de 10% pour frais professionnels</li>
                                <li>Déduisez les cotisations sociales effectivement payées en Suisse</li>
                                <li>Mentionnez l'impôt à la source payé en Suisse pour bénéficier du crédit d'impôt</li>
                            </ol>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="social">
                            <div class="flex justify-between items-center">
                                <span>Quelle assurance maladie choisir en tant que frontalier ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>En tant que frontalier, vous avez le choix entre :</p>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-3">
                                <div class="border p-4 rounded-lg">
                                    <h4 class="font-bold mb-2">LAMal (Suisse)</h4>
                                    <ul class="list-disc pl-5 space-y-1">
                                        <li>Couverture étendue en Suisse</li>
                                        <li>Prise en charge à l'étranger limitée</li>
                                        <li>Coût généralement plus élevé</li>
                                        <li>Délai de résiliation : 3 mois</li>
                                    </ul>
                                </div>
                                <div class="border p-4 rounded-lg">
                                    <h4 class="font-bold mb-2">CMU (France)</h4>
                                    <ul class="list-disc pl-5 space-y-1">
                                        <li>Couverture valable en France et Suisse</li>
                                        <li>Coût généralement moins élevé</li>
                                        <li>Prise en charge à l'étranger limitée</li>
                                        <li>Délai de résiliation : 12 mois</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="retraite">
                            <div class="flex justify-between items-center">
                                <span>Comment fonctionne la retraite pour les travailleurs frontaliers ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Le système de retraite des travailleurs frontaliers est régi par la convention franco-suisse :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Vous cotisez au 1er pilier suisse (AVS) et au 2ème pilier (LPP)</li>
                                <li>Vous cotisez également au système français pour la retraite de base et complémentaire</li>
                                <li>Au moment de la retraite, vous percevrez une pension de chaque pays</li>
                                <li>Le 3ème pilier suisse est un excellent outil d'optimisation fiscale</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="fiscalite">
                            <div class="flex justify-between items-center">
                                <span>Puis-je déduire mes frais de transport entre la France et la Suisse ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Oui, les frais de transport entre votre domicile français et votre lieu de travail en Suisse sont déductibles :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Pour les résidents français : déduction possible dans la limite de 4 872€ par an</li>
                                <li>Pour les quasi-résidents : déduction intégrale des frais réels</li>
                                <li>Conservez tous vos justificatifs (billets, abonnements, factures de carburant)</li>
                                <li>Les frais de péage et de parking sont également déductibles</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="border-b">
                        <div class="faq-question" data-category="documents">
                            <div class="flex justify-between items-center">
                                <span>Quels documents dois-je fournir pour mon dossier fiscal ?</span>
                                <svg class="h-5 w-5 transform transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="faq-answer">
                            <p>Pour constituer un dossier fiscal complet, vous devez fournir :</p>
                            <ul class="list-disc pl-5 mt-2 space-y-1">
                                <li>Votre contrat de travail suisse</li>
                                <li>Vos fiches de salaire de l'année</li>
                                <li>Votre avis d'imposition français de l'année précédente</li>
                                <li>Votre attestation de sécurité sociale</li>
                                <li>Les justificatifs de frais professionnels (transport, repas, formation)</li>
                                <li>Votre attestation de situation familiale</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <div class="mt-8">
                    <h2 class="text-2xl font-bold text-gray-800 mb-4">Vous ne trouvez pas de réponse ?</h2>
                    <div class="bg-gray-50 rounded-lg p-6">
                        <form>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Votre nom</label>
                                    <input type="text" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Votre email</label>
                                    <input type="email" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                </div>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Catégorie de question</label>
                                <select class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]">
                                    <option>Sélectionnez une catégorie...</option>
                                    <option>Fiscalité</option>
                                    <option>Protection sociale</option>
                                    <option>Retraite</option>
                                    <option>Documents</option>
                                    <option>Autre</option>
                                </select>
                            </div>
                            
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-[var(--color-text-secondary)] mb-1">Votre question</label>
                                <textarea rows="4" class="w-full rounded-lg border-[var(--color-border)] p-2 focus:border-[var(--color-secondary)] focus:ring-[var(--color-secondary)]"></textarea>
                            </div>
                            
                            <button class="rounded-lg bg-[var(--color-secondary)] px-4 py-2 text-white hover:bg-[var(--color-primary)] transition-colors">
                                Envoyer votre question
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Gestion des onglets avec animations améliorées
        document.querySelectorAll('.tab-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                const tabId = this.getAttribute('data-tab');

                // Vérifier si l'onglet est déjà actif
                if (this.classList.contains('active')) {
                    return;
                }

                // Masquer tous les contenus d'onglet avec animation
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });

                // Afficher le contenu de l'onglet sélectionné avec un léger délai pour l'animation
                setTimeout(() => {
                    document.getElementById(tabId).classList.add('active');
                }, 100);

                // Mettre à jour l'onglet actif dans la barre latérale
                document.querySelectorAll('.tab-button').forEach(btn => {
                    btn.classList.remove('active');
                    // Réinitialiser les classes obsolètes si elles existent
                    btn.classList.remove('bg-sky-100', 'text-[var(--color-primary)]');
                });

                // Ajouter la classe active au bouton cliqué
                this.classList.add('active');

                // Ajouter un effet de ripple pour le feedback visuel
                createRippleEffect(this, e);
            });

            // Améliorer l'accessibilité avec le clavier
            button.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });
        });

        // Fonction pour créer un effet de ripple au clic
        function createRippleEffect(element, event) {
            const ripple = document.createElement('span');
            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            const x = event.clientX - rect.left - size / 2;
            const y = event.clientY - rect.top - size / 2;

            ripple.style.cssText = `
                position: absolute;
                width: ${size}px;
                height: ${size}px;
                left: ${x}px;
                top: ${y}px;
                background: rgba(49, 130, 206, 0.3);
                border-radius: 50%;
                transform: scale(0);
                animation: ripple 0.6s ease-out;
                pointer-events: none;
                z-index: 1;
            `;

            element.style.position = 'relative';
            element.style.overflow = 'hidden';
            element.appendChild(ripple);

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Ajouter l'animation CSS pour l'effet ripple
        const rippleStyle = document.createElement('style');
        rippleStyle.textContent = `
            @keyframes ripple {
                to {
                    transform: scale(2);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(rippleStyle);

        // Gestion des FAQ
        document.querySelectorAll('.faq-question').forEach(question => {
            question.addEventListener('click', function() {
                const answer = this.nextElementSibling;
                const icon = this.querySelector('svg');
                
                if (answer.style.display === 'block') {
                    answer.style.display = 'none';
                    icon.classList.remove('rotate-180');
                } else {
                    answer.style.display = 'block';
                    icon.classList.add('rotate-180');
                }
            });
        });

        // Zone de drag and drop
        const dropArea = document.querySelector('.drag-drop-area');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            dropArea.classList.add('dragover');
        }
        
        function unhighlight() {
            dropArea.classList.remove('dragover');
        }
        
        dropArea.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles(files);
        }
        
        function handleFiles(files) {
            alert(`Vous avez déposé ${files.length} fichier(s)`);
        }

        // Simulator JavaScript Functions
        function showSimulatorForm() {
            document.getElementById('simulator-home').style.display = 'none';
            document.getElementById('simulator-form').style.display = 'block';
            document.getElementById('simulator-form').scrollIntoView({ behavior: 'smooth' });
        }

        // Gestion des étapes du formulaire
        function nextStep(step) {
            document.querySelector('.simulator-form-step.active').classList.remove('active');
            document.getElementById(`step${step}`).classList.add('active');

            // Mise à jour de la barre de progression
            const progress = document.getElementById('formProgress');
            progress.style.width = `${step * 33}%`;

            // Faire défiler jusqu'au formulaire
            document.getElementById('simulator-form').scrollIntoView({ behavior: 'smooth' });
        }

        function prevStep(step) {
            document.querySelector('.simulator-form-step.active').classList.remove('active');
            document.getElementById(`step${step}`).classList.add('active');

            // Mise à jour de la barre de progression
            const progress = document.getElementById('formProgress');
            progress.style.width = `${step * 33}%`;
        }

        // Affichage des options avancées
        function toggleAdvanced() {
            const advancedOptions = document.getElementById('advancedOptions');
            const toggleIcon = document.querySelector('.simulator-advanced-toggle i');

            if (advancedOptions.style.display === 'block') {
                advancedOptions.style.display = 'none';
                toggleIcon.className = 'fas fa-cog';
            } else {
                advancedOptions.style.display = 'block';
                toggleIcon.className = 'fas fa-times';
            }
        }

        // Affichage des détails des résultats
        function toggleDetails(id) {
            const details = document.getElementById(id);
            if (details.style.display === 'block') {
                details.style.display = 'none';
            } else {
                details.style.display = 'block';
            }
        }

        // Calcul des résultats
        function calculateResults() {
            // Simulation de calcul
            document.getElementById('simulator-form').style.display = 'none';
            document.getElementById('simulator-results').style.display = 'block';
            document.getElementById('simulator-results').scrollIntoView({ behavior: 'smooth' });
        }

        // Affichage du rapport
        function showReport() {
            document.getElementById('simulator-results').style.display = 'none';
            document.getElementById('simulator-report').style.display = 'block';
            document.getElementById('simulator-report').scrollIntoView({ behavior: 'smooth' });
        }

        // Gestion de la FAQ du simulateur
        function toggleFAQ(element) {
            const faqItem = element.parentElement;
            faqItem.classList.toggle('active');

            const icon = element.querySelector('i');
            if (faqItem.classList.contains('active')) {
                icon.className = 'fas fa-chevron-up';
            } else {
                icon.className = 'fas fa-chevron-down';
            }
        }

        // Affichage des âges des enfants si nécessaire
        document.addEventListener('DOMContentLoaded', function() {
            const childrenCountInput = document.getElementById('childrenCount');
            if (childrenCountInput) {
                childrenCountInput.addEventListener('change', function() {
                    const childrenAges = document.getElementById('childrenAges');
                    if (this.value > 0) {
                        childrenAges.style.display = 'block';
                    } else {
                        childrenAges.style.display = 'none';
                    }
                });
            }
        });
    </script>
</body>
</html>