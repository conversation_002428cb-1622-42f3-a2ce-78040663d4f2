<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Integration</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
    </style>
</head>
<body>
    <h1>Test d'intégration du simulateur LAMal vs CMU</h1>
    
    <div id="test-results"></div>
    
    <script>
        function runTests() {
            const results = document.getElementById('test-results');
            
            // Test 1: Vérifier que la page principale se charge
            try {
                const socialTab = parent.document.getElementById('social');
                if (socialTab) {
                    addResult('✓ Onglet Social trouvé', 'success');
                } else {
                    addResult('✗ Onglet Social non trouvé', 'error');
                }
            } catch (e) {
                addResult('Test exécuté dans une iframe - normal', 'info');
            }
            
            // Test 2: Vérifier les éléments du simulateur
            const simulatorElements = [
                'simulator-container',
                'simulator-home',
                'simulator-form',
                'simulator-results',
                'simulator-report',
                'simulator-faq'
            ];
            
            simulatorElements.forEach(elementId => {
                try {
                    const element = parent.document.getElementById(elementId);
                    if (element) {
                        addResult(`✓ Élément ${elementId} trouvé`, 'success');
                    } else {
                        addResult(`✗ Élément ${elementId} non trouvé`, 'error');
                    }
                } catch (e) {
                    addResult(`Test ${elementId} - exécuté dans iframe`, 'info');
                }
            });
            
            // Test 3: Vérifier les fonctions JavaScript
            const functions = [
                'showSimulatorForm',
                'nextStep',
                'prevStep',
                'toggleAdvanced',
                'toggleDetails',
                'calculateResults',
                'showReport',
                'toggleFAQ'
            ];
            
            functions.forEach(funcName => {
                try {
                    if (typeof parent.window[funcName] === 'function') {
                        addResult(`✓ Fonction ${funcName} disponible`, 'success');
                    } else {
                        addResult(`✗ Fonction ${funcName} non disponible`, 'error');
                    }
                } catch (e) {
                    addResult(`Test fonction ${funcName} - exécuté dans iframe`, 'info');
                }
            });
            
            addResult('Tests terminés. Vérifiez manuellement la navigation entre onglets et les fonctionnalités du simulateur.', 'info');
        }
        
        function addResult(message, type) {
            const results = document.getElementById('test-results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            results.appendChild(div);
        }
        
        // Exécuter les tests au chargement
        window.onload = runTests;
    </script>
</body>
</html>
