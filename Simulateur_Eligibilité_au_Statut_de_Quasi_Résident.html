<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulateur d'Éligibilité au Statut de Quasi-Résident</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .input-highlight {
            transition: all 0.3s ease;
        }
        .input-highlight:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .tooltip-text {
            visibility: hidden;
            opacity: 0;
            transition: opacity 0.3s;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen font-sans">
    <!-- Header -->
    <header class="bg-blue-700 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <div class="mb-4 md:mb-0">
                    <h1 class="text-2xl md:text-3xl font-bold">Simulateur d'Éligibilité au Statut de Quasi-Résident</h1>
                    <p class="text-blue-100 mt-2">Estimez votre éligibilité au statut fiscal avantageux pour travailleurs frontaliers</p>
                </div>
                <div class="bg-white text-blue-800 px-4 py-2 rounded-lg shadow">
                    <p class="font-semibold">Cantons supportés : Genève & Vaud</p>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="container mx-auto px-4 py-8">
        <!-- Introduction Section -->
        <section id="intro" class="bg-white rounded-xl shadow-md p-6 mb-8 fade-in">
            <div class="flex items-start">
                <div class="mr-4 text-blue-600">
                    <i class="fas fa-info-circle text-2xl"></i>
                </div>
                <div>
                    <h2 class="text-xl font-bold text-gray-800 mb-3">Qu'est-ce que le statut de quasi-résident ?</h2>
                    <p class="text-gray-600 mb-4">
                        Le statut de quasi-résident en Suisse permet aux travailleurs frontaliers de bénéficier d'avantages fiscaux similaires aux résidents suisses, sous condition qu'au <span class="font-semibold"> moins 90% de leurs revenus mondiaux</span> soient imposés en Suisse.
                    </p>
                    <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
                        <div class="flex">
                            <div class="flex-shrink-0 text-yellow-500">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm text-yellow-700">
                                    <span class="font-bold">Attention :</span> Ce simulateur fournit une estimation préliminaire. La décision finale doit être prise avec un expert fiscal. Le choix du statut est irrévocable pour l'année concernée.
                                </p>
                            </div>
                        </div>
                    </div>
                    <button onclick="startSimulation()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 flex items-center">
                        Commencer la simulation
                        <i class="fas fa-arrow-right ml-2"></i>
                    </button>
                </div>
            </div>
        </section>

        <!-- Simulation Form (Hidden by default) -->
        <section id="simulation-form" class="hidden bg-white rounded-xl shadow-md p-6 mb-8 fade-in">
            <div class="flex items-center mb-6">
                <div class="bg-blue-100 p-2 rounded-full mr-4">
                    <i class="fas fa-calculator text-blue-600 text-xl"></i>
                </div>
                <h2 class="text-xl font-bold text-gray-800">Votre situation fiscale</h2>
            </div>

            <form id="eligibility-form" class="space-y-6">
                <!-- Section A: Situation Personnelle -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-2 text-sm">1</span>
                        Votre situation familiale
                        <span class="text-sm font-normal text-gray-500 ml-2">(inclut votre conjoint si marié)</span>
                        <div class="tooltip ml-1 relative">
                            <i class="fas fa-question-circle text-gray-400 hover:text-gray-600"></i>
                            <div class="tooltip-text absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 -left-32 -top-12">
                                Le statut familial détermine si les revenus de votre conjoint doivent être pris en compte dans le calcul
                            </div>
                        </div>
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="family-status" class="block text-sm font-medium text-gray-700 mb-1">Situation familiale</label>
                            <select id="family-status" name="family-status" class="input-highlight mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md border">
                                <option value="single">Célibataire</option>
                                <option value="married">Marié/Pacsé</option>
                                <option value="divorced">Divorcé/Séparé</option>
                                <option value="widowed">Veuf/Veuve</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Section B: Revenus en Suisse -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-2 text-sm">2</span>
                        Vos revenus imposables en Suisse (en CHF)
                        <span class="text-sm font-normal text-gray-500 ml-2">(salaires bruts avant impôt)</span>
                        <div class="tooltip ml-1 relative">
                            <i class="fas fa-question-circle text-gray-400 hover:text-gray-600"></i>
                            <div class="tooltip-text absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 -left-32 -top-12">
                                Inclure tous les revenus soumis à l'impôt à la source en Suisse (salaires, primes, bonus)
                            </div>
                        </div>
                    </h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="income-switzerland" class="block text-sm font-medium text-gray-700 mb-1">Vos revenus bruts annuels</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">CHF</span>
                                </div>
                                <input type="number" id="income-switzerland" name="income-switzerland" class="input-highlight focus:ring-blue-500 focus:border-blue-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md py-2 border" placeholder="120000">
                            </div>
                        </div>
                        <div id="spouse-income-container" class="hidden">
                            <label for="income-spouse" class="block text-sm font-medium text-gray-700 mb-1">Revenus bruts annuels de votre conjoint</label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">CHF</span>
                                </div>
                                <input type="number" id="income-spouse" name="income-spouse" class="input-highlight focus:ring-blue-500 focus:border-blue-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md py-2 border" placeholder="0.00">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Section C: Revenus mondiaux -->
                <div class="border-b border-gray-200 pb-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                        <span class="bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center mr-2 text-sm">3</span>
                        Vos autres revenus mondiaux (en CHF)
                        <span class="text-sm font-normal text-gray-500 ml-2">(y compris revenus locatifs)</span>
                        <div class="tooltip ml-1 relative">
                            <i class="fas fa-question-circle text-gray-400 hover:text-gray-600"></i>
                            <div class="tooltip-text absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 -left-32 -top-12">
                                Tous revenus perçus hors de Suisse (salaires étrangers, dividendes, intérêts, pensions, etc.)
                            </div>
                        </div>
                    </h3>
                    <div class="bg-blue-50 p-4 rounded-lg mb-4">
                        <p class="text-sm text-blue-800 flex items-start">
                            <i class="fas fa-info-circle mr-2 mt-1 flex-shrink-0"></i>
                            <span>Veuillez convertir tous les revenus en CHF. Incluez tous les revenus bruts de votre foyer (salaires, rentes, revenus locatifs, etc.), où qu'ils soient perçus.</span>
                        </p>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="income-foreign" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                Revenus hors Suisse <span class="text-gray-500 font-normal ml-1">(salaires, pensions, etc.)</span>
                                <div class="tooltip ml-1 relative">
                                    <i class="fas fa-question-circle text-gray-400 hover:text-gray-600"></i>
                                    <div class="tooltip-text absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 -left-32 -top-12">
                                        Revenus perçus dans d'autres pays que la Suisse, même s'ils sont imposés localement
                                    </div>
                                </div>
                            </label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">CHF</span>
                                </div>
                                <input type="number" id="income-foreign" name="income-foreign" class="input-highlight focus:ring-blue-500 focus:border-blue-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md py-2 border" placeholder="25000">
                            </div>
                        </div>
                        <div>
                            <label for="rental-primary" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                Valeur locative résidence principale
                                <div class="tooltip ml-1 relative">
                                    <i class="fas fa-question-circle text-gray-400 hover:text-gray-600"></i>
                                    <div class="tooltip-text absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 -left-32 -top-12">
                                        Estimation annuelle de la valeur locative si vous êtes propriétaire de votre résidence principale
                                    </div>
                                </div>
                            </label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">CHF</span>
                                </div>
                                <input type="number" id="rental-primary" name="rental-primary" class="input-highlight focus:ring-blue-500 focus:border-blue-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md py-2 border" placeholder="15000">
                            </div>
                        </div>
                        <div>
                            <label for="rental-secondary" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                Valeur locative résidence secondaire
                                <div class="tooltip ml-1 relative">
                                    <i class="fas fa-question-circle text-gray-400 hover:text-gray-600"></i>
                                    <div class="tooltip-text absolute z-10 w-64 bg-gray-800 text-white text-xs rounded p-2 -left-32 -top-12">
                                        Estimation annuelle de la valeur locative si vous possédez une résidence secondaire
                                    </div>
                                </div>
                            </label>
                            <div class="mt-1 relative rounded-md shadow-sm">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <span class="text-gray-500 sm:text-sm">CHF</span>
                                </div>
                                <input type="number" id="rental-secondary" name="rental-secondary" class="input-highlight focus:ring-blue-500 focus:border-blue-500 block w-full pl-12 pr-12 sm:text-sm border-gray-300 rounded-md py-2 border" placeholder="8000">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="flex flex-col sm:flex-row justify-between gap-4 pt-4">
                    <button type="button" onclick="resetForm()" class="bg-gray-200 hover:bg-gray-300 text-gray-800 font-bold py-3 px-6 rounded-lg shadow transition duration-300 flex items-center justify-center">
                        <i class="fas fa-redo mr-2"></i> Réinitialiser
                    </button>
                    <button type="button" onclick="calculateEligibility()" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 flex items-center justify-center">
                        <i class="fas fa-calculator mr-2"></i> Calculer mon éligibilité
                    </button>
                </div>
            </form>
        </section>

        <!-- Results Section - Eligible (Hidden by default) -->
        <section id="result-eligible" class="hidden bg-white rounded-xl shadow-md p-6 mb-8 fade-in">
            <div class="flex flex-col items-center text-center mb-6">
                <div class="bg-green-100 p-3 rounded-full mb-4">
                    <i class="fas fa-check-circle text-green-600 text-4xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">✅ Éligible au statut de quasi-résident</h2>
                <p class="text-gray-600 max-w-2xl">
                    Selon les informations fournies, vous remplissez le critère principal des 90% de revenus du foyer imposés en Suisse.
                </p>
                <div class="mt-4 bg-blue-50 px-4 py-2 rounded-lg">
                    <p class="text-blue-800 font-medium">Ratio calculé : <span id="eligible-ratio" class="font-bold text-lg">90%</span></p>
                </div>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-8">
                <div class="bg-green-50 p-5 rounded-lg border border-green-100">
                    <h3 class="text-lg font-semibold text-green-800 mb-3 flex items-center">
                        <i class="fas fa-star mr-2"></i> Vos avantages potentiels
                    </h3>
                    <ul class="space-y-2 text-green-700">
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                            <span>Déduction du 3ème pilier (prévoyance privée)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                            <span>Déduction des frais réels (frais professionnels)</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                            <span>Imposition sur le revenu net après déductions</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-check-circle text-green-500 mt-1 mr-2 flex-shrink-0"></i>
                            <span>Accès à des taux d'imposition plus avantageux</span>
                        </li>
                    </ul>
                </div>

                <div class="bg-blue-50 p-5 rounded-lg border border-blue-100">
                    <h3 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                        <i class="fas fa-arrow-right mr-2"></i> Prochaines étapes
                    </h3>
                    <div class="space-y-4">
                        <p class="text-blue-700">
                            Bien que ce simulateur indique une éligibilité potentielle, nous vous recommandons fortement de consulter un expert fiscal pour :
                        </p>
                        <ul class="space-y-2 text-blue-700">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                                <span>Valider précisément votre situation</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                                <span>Optimiser votre déclaration fiscale</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                                <span>Effectuer la demande auprès des autorités</span>
                            </li>
                        </ul>
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0 text-yellow-500">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        <span class="font-bold">Important :</span> Le choix du statut de quasi-résident est irrévocable pour l'année concernée. Une analyse approfondie est essentielle.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <button class="w-full bg-green-600 hover:bg-green-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 flex items-center justify-center mt-4">
                            <i class="fas fa-user-tie mr-2"></i> Prendre rendez-vous avec un expert
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-8 text-center">
                <button onclick="newSimulation()" class="text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center mx-auto">
                    <i class="fas fa-redo mr-2"></i> Effectuer une nouvelle simulation
                </button>
            </div>
        </section>

        <!-- Results Section - Not Eligible (Hidden by default) -->
        <section id="result-not-eligible" class="hidden bg-white rounded-xl shadow-md p-6 mb-8 fade-in">
            <div class="flex flex-col items-center text-center mb-6">
                <div class="bg-red-100 p-3 rounded-full mb-4">
                    <i class="fas fa-times-circle text-red-600 text-4xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-2">❌ Non éligible au statut de quasi-résident</h2>
                <p class="text-gray-600 max-w-2xl">
                    Selon les informations fournies, vos revenus imposables en Suisse représentent <span id="non-eligible-ratio" class="font-bold">XX%</span> de vos revenus mondiaux, ce qui est inférieur au seuil requis de 90%.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-6 mt-8">
                <div class="bg-red-50 p-5 rounded-lg border border-red-100">
                    <h3 class="text-lg font-semibold text-red-800 mb-3 flex items-center">
                        <i class="fas fa-exclamation-triangle mr-2"></i> Principales raisons
                    </h3>
                    <ul class="space-y-2 text-red-700">
                        <li class="flex items-start">
                            <i class="fas fa-circle text-red-500 mt-2 mr-2 text-xs flex-shrink-0"></i>
                            <span>Vos revenus hors Suisse sont trop importants par rapport à vos revenus suisses</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-red-500 mt-2 mr-2 text-xs flex-shrink-0"></i>
                            <span>La valeur locative de vos biens immobiliers réduit votre ratio</span>
                        </li>
                        <li class="flex items-start">
                            <i class="fas fa-circle text-red-500 mt-2 mr-2 text-xs flex-shrink-0"></i>
                            <span>Si vous êtes marié, les revenus de votre conjoint hors Suisse sont pris en compte</span>
                        </li>
                    </ul>
                    <div class="mt-4 bg-gray-100 p-3 rounded-lg">
                        <p class="text-sm text-gray-700">
                            <span class="font-semibold">Note :</span> Ce statut est réévalué chaque année. Une modification de votre situation (augmentation de salaire en Suisse, diminution des revenus étrangers) pourrait changer votre éligibilité.
                        </p>
                    </div>
                </div>

                <div class="bg-blue-50 p-5 rounded-lg border border-blue-100">
                    <h3 class="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                        <i class="fas fa-lightbulb mr-2"></i> Que faire ?
                    </h3>
                    <div class="space-y-4">
                        <p class="text-blue-700">
                            Bien que non éligible selon ce calcul, un expert fiscal pourrait vous aider à :
                        </p>
                        <ul class="space-y-2 text-blue-700">
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                                <span>Vérifier l'exactitude du calcul</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                                <span>Explorer d'autres optimisations fiscales</span>
                            </li>
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-blue-500 mt-1 mr-2 flex-shrink-0"></i>
                                <span>Planifier une stratégie pour améliorer votre ratio</span>
                            </li>
                        </ul>
                        <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                            <div class="flex">
                                <div class="flex-shrink-0 text-yellow-500">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-yellow-700">
                                        <span class="font-bold">Conseil :</span> Si votre conjoint ne travaille pas en Suisse, son absence de revenus suisses impacte négativement votre ratio. Une activité même réduite en Suisse pourrait améliorer votre situation.
                                    </p>
                                </div>
                            </div>
                        </div>
                        <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-lg shadow-md transition duration-300 flex items-center justify-center mt-4">
                            <i class="fas fa-user-tie mr-2"></i> Consulter un expert fiscal
                        </button>
                    </div>
                </div>
            </div>

            <div class="mt-8 text-center">
                <button onclick="newSimulation()" class="text-blue-600 hover:text-blue-800 font-medium flex items-center justify-center mx-auto">
                    <i class="fas fa-redo mr-2"></i> Effectuer une nouvelle simulation
                </button>
            </div>
        </section>
    </main>

    <script>
        // Show/hide spouse income field based on family status
        document.getElementById('family-status').addEventListener('change', function() {
            const spouseIncomeContainer = document.getElementById('spouse-income-container');
            if (this.value === 'married') {
                spouseIncomeContainer.classList.remove('hidden');
            } else {
                spouseIncomeContainer.classList.add('hidden');
                document.getElementById('income-spouse').value = '';
            }
        });

        // Start simulation - hide intro, show form
        function startSimulation() {
            document.getElementById('intro').classList.add('hidden');
            document.getElementById('simulation-form').classList.remove('hidden');
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Reset form
        function resetForm() {
            document.getElementById('eligibility-form').reset();
            document.getElementById('spouse-income-container').classList.add('hidden');
        }

        // New simulation - show form, hide results
        function newSimulation() {
            document.getElementById('simulation-form').classList.remove('hidden');
            document.getElementById('result-eligible').classList.add('hidden');
            document.getElementById('result-not-eligible').classList.add('hidden');
            resetForm();
            window.scrollTo({ top: 0, behavior: 'smooth' });
        }

        // Calculate eligibility
        function calculateEligibility() {
            // Get form values
            const familyStatus = document.getElementById('family-status').value;
            const incomeSwitzerland = parseFloat(document.getElementById('income-switzerland').value) || 0;
            const incomeSpouse = familyStatus === 'married' ? (parseFloat(document.getElementById('income-spouse').value) || 0) : 0;
            const incomeForeign = parseFloat(document.getElementById('income-foreign').value) || 0;
            const rentalPrimary = parseFloat(document.getElementById('rental-primary').value) || 0;
            const rentalSecondary = parseFloat(document.getElementById('rental-secondary').value) || 0;

            // Validate required fields
            if (incomeSwitzerland <= 0) {
                alert('Veuillez saisir vos revenus en Suisse');
                return;
            }

            // Calculate totals
            const totalRevenusSuisse = incomeSwitzerland + incomeSpouse;
            const totalRevenusMondial = totalRevenusSuisse + incomeForeign + rentalPrimary + rentalSecondary;

            // Calculate ratio
            let ratioSuisse = 0;
            if (totalRevenusMondial > 0) {
                ratioSuisse = (totalRevenusSuisse / totalRevenusMondial) * 100;
            }

            // Round to 1 decimal
            ratioSuisse = Math.round(ratioSuisse * 10) / 10;

            // Show appropriate result
            document.getElementById('simulation-form').classList.add('hidden');
            
            if (ratioSuisse >= 90) {
                document.getElementById('eligible-ratio').textContent = ratioSuisse + '%';
                document.getElementById('result-eligible').classList.remove('hidden');
            } else {
                document.getElementById('non-eligible-ratio').textContent = ratioSuisse + '%';
                document.getElementById('result-not-eligible').classList.remove('hidden');
            }

            window.scrollTo({ top: 0, behavior: 'smooth' });
        }
    </script>
</body>
</html>
