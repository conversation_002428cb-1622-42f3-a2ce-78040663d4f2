<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simulateur LAMal vs. CMU | Travailleurs Frontaliers</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* Palette de couleurs */
            --primary: #2a5b8c; /* Bleu profond */
            --secondary: #4a90e2; /* Bleu clair */
            --accent: #ff6b35; /* Orange pour CTA */
            --success: #2e8b57; /* Vert pour validation */
            --light: #f8f9fa; /* Arrière-plan clair */
            --dark: #1d2d35; /* Texte sombre */
            --grey: #6c757d; /* Texte secondaire */
            --border: #dee2e6; /* Bordures */
            --warning: #ffc107; /* Jaune pour avertissements */
            
            /* Typographie */
            --font-main: 'Segoe UI', Roboto, 'Helvetica Neue', sans-serif;
            --font-heading: 'Montserrat', 'Arial Rounded MT Bold', sans-serif;
            
            /* Espacements */
            --space-xs: 0.5rem;
            --space-sm: 1rem;
            --space-md: 1.5rem;
            --space-lg: 2rem;
            --space-xl: 3rem;
            
            /* Arrondis */
            --radius-sm: 4px;
            --radius-md: 8px;
            --radius-lg: 12px;
            --radius-xl: 20px;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: var(--font-main);
            color: var(--dark);
            background-color: var(--light);
            line-height: 1.6;
        }
        
        /* Header */
        header {
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            position: sticky;
            top: 0;
            z-index: 1000;
        }
        
        .header-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--space-sm);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .logo {
            display: flex;
            align-items: center;
            gap: var(--space-sm);
            font-family: var(--font-heading);
            font-weight: 700;
            color: var(--primary);
            text-decoration: none;
            font-size: 1.2rem;
        }
        
        .logo i {
            font-size: 1.5rem;
        }
        
        nav ul {
            display: flex;
            list-style: none;
            gap: var(--space-lg);
        }
        
        nav a {
            text-decoration: none;
            color: var(--grey);
            font-weight: 500;
            transition: color 0.3s;
        }
        
        nav a:hover, nav a.active {
            color: var(--primary);
        }
        
        .mobile-menu-btn {
            display: none;
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: var(--primary);
        }
        
        /* Sections principales */
        section {
            padding: var(--space-xl) 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-md);
        }
        
        /* Page d'accueil */
        .hero {
            background: linear-gradient(135deg, rgba(42, 91, 140, 0.95), rgba(74, 144, 226, 0.85)), url('https://images.unsplash.com/photo-1579621970563-ebec7560ff3e?ixlib=rb-4.0.3&auto=format&fit=crop&w=1951&q=80');
            background-size: cover;
            background-position: center;
            color: white;
            text-align: center;
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            margin: var(--space-md);
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: var(--space-sm);
            font-family: var(--font-heading);
        }
        
        .hero p {
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto var(--space-lg);
            opacity: 0.9;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--space-md);
            margin: var(--space-xl) 0;
        }
        
        .feature {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            text-align: center;
            transition: transform 0.3s;
        }
        
        .feature:hover {
            transform: translateY(-5px);
        }
        
        .feature i {
            font-size: 2.5rem;
            color: var(--primary);
            margin-bottom: var(--space-sm);
        }
        
        .feature h3 {
            margin-bottom: var(--space-xs);
            color: var(--primary);
        }
        
        .warning {
            background: rgba(255, 193, 7, 0.1);
            border-left: 4px solid var(--warning);
            padding: var(--space-md);
            border-radius: var(--radius-sm);
            margin: var(--space-lg) 0;
        }
        
        .btn {
            display: inline-block;
            padding: 0.8rem 2rem;
            background-color: var(--accent);
            color: white;
            border-radius: var(--radius-md);
            text-decoration: none;
            font-weight: 600;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 1rem;
        }
        
        .btn:hover {
            background-color: #e55a2b;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
        }
        
        .btn-secondary {
            background-color: var(--secondary);
        }
        
        .btn-secondary:hover {
            background-color: #3a7fd9;
        }
        
        /* Formulaire */
        .form-container {
            background: white;
            border-radius: var(--radius-xl);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            margin: var(--space-lg) 0;
        }
        
        .form-header {
            padding: var(--space-md);
            background: var(--primary);
            color: white;
            text-align: center;
        }
        
        .progress-bar {
            height: 8px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 4px;
            margin-top: var(--space-sm);
            overflow: hidden;
        }
        
        .progress {
            height: 100%;
            background: white;
            width: 33%;
            border-radius: 4px;
            transition: width 0.5s;
        }
        
        .form-body {
            padding: var(--space-lg);
        }
        
        .form-step {
            display: none;
        }
        
        .form-step.active {
            display: block;
        }
        
        .form-group {
            margin-bottom: var(--space-lg);
        }
        
        .form-group h3 {
            margin-bottom: var(--space-md);
            color: var(--primary);
            border-bottom: 1px solid var(--border);
            padding-bottom: var(--space-xs);
        }
        
        label {
            display: block;
            margin-bottom: var(--space-xs);
            font-weight: 500;
        }
        
        .input-group {
            margin-bottom: var(--space-md);
        }
        
        .info-tooltip {
            display: inline-block;
            margin-left: var(--space-xs);
            color: var(--secondary);
            cursor: pointer;
            position: relative;
        }
        
        .tooltip-text {
            position: absolute;
            bottom: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark);
            color: white;
            padding: var(--space-sm);
            border-radius: var(--radius-md);
            width: 250px;
            font-size: 0.9rem;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s;
            z-index: 10;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .info-tooltip:hover .tooltip-text {
            opacity: 1;
            visibility: visible;
        }
        
        input, select {
            width: 100%;
            padding: 0.8rem;
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            font-family: var(--font-main);
            font-size: 1rem;
        }
        
        input:focus, select:focus {
            outline: none;
            border-color: var(--secondary);
            box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
        }
        
        .checkbox-group {
            display: flex;
            gap: var(--space-sm);
            align-items: center;
            margin-bottom: var(--space-xs);
        }
        
        .advanced-toggle {
            display: flex;
            align-items: center;
            gap: var(--space-xs);
            color: var(--primary);
            font-weight: 500;
            cursor: pointer;
            margin-top: var(--space-sm);
        }
        
        .advanced-options {
            display: none;
            margin-top: var(--space-md);
            padding-top: var(--space-md);
            border-top: 1px dashed var(--border);
        }
        
        .slider-container {
            margin-top: var(--space-sm);
        }
        
        .slider {
            width: 100%;
            height: 8px;
            -webkit-appearance: none;
            appearance: none;
            background: var(--border);
            outline: none;
            border-radius: 4px;
            margin: 1rem 0;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: var(--primary);
            cursor: pointer;
        }
        
        .slider-labels {
            display: flex;
            justify-content: space-between;
            font-size: 0.9rem;
            color: var(--grey);
        }
        
        .form-navigation {
            display: flex;
            justify-content: space-between;
            margin-top: var(--space-lg);
        }
        
        /* Résultats */
        .results-header {
            text-align: center;
            margin-bottom: var(--space-xl);
        }
        
        .results-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--space-lg);
            margin-bottom: var(--space-xl);
        }
        
        .card {
            background: white;
            border-radius: var(--radius-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            padding: var(--space-lg);
            text-align: center;
            position: relative;
            transition: all 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }
        
        .card.recommended {
            border: 2px solid var(--success);
            transform: scale(1.02);
        }
        
        .card.recommended::after {
            content: 'Recommandé';
            position: absolute;
            top: -12px;
            right: 20px;
            background: var(--success);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: var(--radius-md);
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .card-header {
            padding-bottom: var(--space-md);
            margin-bottom: var(--space-md);
            border-bottom: 1px solid var(--border);
        }
        
        .price {
            font-size: 2.5rem;
            font-weight: 700;
            margin: var(--space-sm) 0;
            color: var(--primary);
        }
        
        .price-period {
            font-size: 1rem;
            color: var(--grey);
            font-weight: 400;
        }
        
        .details-toggle {
            color: var(--secondary);
            font-weight: 500;
            cursor: pointer;
            margin: var(--space-sm) 0;
            display: inline-block;
        }
        
        .details-content {
            display: none;
            text-align: left;
            margin-top: var(--space-md);
            background: #f8fafc;
            padding: var(--space-md);
            border-radius: var(--radius-md);
        }
        
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: var(--space-lg) 0;
        }
        
        .comparison-table th, 
        .comparison-table td {
            padding: 0.8rem;
            text-align: left;
            border-bottom: 1px solid var(--border);
        }
        
        .comparison-table th {
            font-weight: 600;
            color: var(--primary);
        }
        
        .comparison-table tr:last-child td {
            border-bottom: none;
        }
        
        .threshold-graph {
            background: white;
            border-radius: var(--radius-lg);
            padding: var(--space-lg);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: var(--space-lg);
        }
        
        .graph-container {
            height: 100px;
            background: linear-gradient(to right, var(--secondary), var(--accent));
            border-radius: var(--radius-md);
            margin: var(--space-md) 0;
            position: relative;
        }
        
        .threshold-marker {
            position: absolute;
            top: -25px;
            width: 2px;
            height: 150px;
            background: var(--dark);
        }
        
        .threshold-label {
            position: absolute;
            top: -45px;
            left: 50%;
            transform: translateX(-50%);
            background: var(--dark);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: var(--radius-md);
            white-space: nowrap;
        }
        
        .current-position {
            position: absolute;
            bottom: -25px;
            transform: translateX(-50%);
            background: var(--accent);
            color: white;
            padding: 0.3rem 0.8rem;
            border-radius: var(--radius-md);
            font-weight: 600;
        }
        
        .graph-labels {
            display: flex;
            justify-content: space-between;
            color: var(--grey);
            font-size: 0.9rem;
        }
        
        /* Rapport */
        .report-container {
            text-align: center;
            padding: var(--space-xl) 0;
        }
        
        .report-card {
            background: white;
            border-radius: var(--radius-xl);
            padding: var(--space-xl);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
            max-width: 800px;
            margin: 0 auto;
            position: relative;
        }
        
        .partners {
            display: flex;
            justify-content: center;
            gap: var(--space-lg);
            margin: var(--space-xl) 0;
            flex-wrap: wrap;
        }
        
        .partner-logo {
            height: 60px;
            filter: grayscale(100%);
            opacity: 0.7;
            transition: all 0.3s;
        }
        
        .partner-logo:hover {
            filter: grayscale(0);
            opacity: 1;
        }
        
        /* FAQ */
        .faq-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .faq-item {
            margin-bottom: var(--space-sm);
            border: 1px solid var(--border);
            border-radius: var(--radius-md);
            overflow: hidden;
        }
        
        .faq-question {
            padding: var(--space-md);
            background: white;
            font-weight: 600;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .faq-answer {
            padding: 0 var(--space-md);
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s, padding 0.3s;
            background: #f8fafc;
        }
        
        .faq-item.active .faq-answer {
            padding: var(--space-md);
            max-height: 500px;
        }
        
        /* Footer */
        footer {
            background: var(--dark);
            color: white;
            padding: var(--space-xl) 0;
        }
        
        .footer-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-lg);
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 var(--space-md);
        }
        
        .footer-logo {
            font-family: var(--font-heading);
            font-weight: 700;
            font-size: 1.5rem;
            margin-bottom: var(--space-sm);
            display: inline-block;
        }
        
        .footer-links h3 {
            margin-bottom: var(--space-md);
            font-size: 1.1rem;
        }
        
        .footer-links ul {
            list-style: none;
        }
        
        .footer-links li {
            margin-bottom: var(--space-xs);
        }
        
        .footer-links a {
            color: #a0aec0;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .footer-links a:hover {
            color: white;
        }
        
        .copyright {
            text-align: center;
            padding-top: var(--space-lg);
            margin-top: var(--space-lg);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #a0aec0;
            font-size: 0.9rem;
        }
        
        /* Responsive */
        @media (max-width: 992px) {
            .features {
                grid-template-columns: repeat(2, 1fr);
            }
        }
        
        @media (max-width: 768px) {
            .mobile-menu-btn {
                display: block;
            }
            
            nav ul {
                display: none;
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                flex-direction: column;
                padding: var(--space-md);
                box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
            }
            
            nav.active ul {
                display: flex;
            }
            
            .hero {
                padding: var(--space-lg);
            }
            
            .hero h1 {
                font-size: 2rem;
            }
            
            .features {
                grid-template-columns: 1fr;
            }
            
            .form-container {
                margin: var(--space-md) 0;
            }
            
            .results-container {
                grid-template-columns: 1fr;
            }
        }
        
        @media (max-width: 576px) {
            .hero {
                margin: var(--space-sm);
                padding: var(--space-lg) var(--space-md);
            }
            
            .hero h1 {
                font-size: 1.8rem;
            }
            
            .btn {
                width: 100%;
            }
            
            .form-navigation {
                flex-direction: column;
                gap: var(--space-sm);
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header>
        <div class="header-container">
            <a href="#" class="logo">
                <i class="fas fa-balance-scale"></i>
                <span>FrontalierAssurance</span>
            </a>
            <button class="mobile-menu-btn" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <nav id="mainNav">
                <ul>
                    <li><a href="#" class="active">Simulateur</a></li>
                    <li><a href="#faq">FAQ</a></li>
                    <li><a href="#">À propos</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Page d'accueil -->
    <section id="home">
        <div class="container">
            <div class="hero">
                <h1>Simulateur LAMal vs. CMU pour frontaliers</h1>
                <p>Obtenez une comparaison personnalisée, fiable et 100% gratuite basée sur les chiffres officiels 2025</p>
                <a href="#simulator" class="btn">Démarrer ma simulation</a>
            </div>
            
            <div class="features">
                <div class="feature">
                    <i class="fas fa-calculator"></i>
                    <h3>Calculs Précis</h3>
                    <p>Basés sur les données officielles 2025 pour des résultats fiables</p>
                </div>
                <div class="feature">
                    <i class="fas fa-bolt"></i>
                    <h3>Résultats Instantanés</h3>
                    <p>Comparaison immédiate avec visualisation claire des données</p>
                </div>
                <div class="feature">
                    <i class="fas fa-file-pdf"></i>
                    <h3>Rapport Détaillé</h3>
                    <p>Téléchargez votre analyse personnalisée pour prendre votre décision</p>
                </div>
            </div>
            
            <div class="warning">
                <p><strong>Important :</strong> Votre choix d'assurance maladie est obligatoire et irrévocable pendant toute l'année civile. Cette décision impacte votre protection santé et votre budget. Prenez le temps de bien comparer les options.</p>
            </div>
        </div>
    </section>

    <!-- Simulateur -->
    <section id="simulator">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: var(--space-lg);">Simulez votre assurance maladie</h2>
            
            <div class="form-container">
                <div class="form-header">
                    <h3>Votre situation personnelle</h3>
                    <div class="progress-bar">
                        <div class="progress" id="formProgress"></div>
                    </div>
                </div>
                
                <div class="form-body">
                    <!-- Étape 1: Votre Situation -->
                    <div class="form-step active" id="step1">
                        <div class="form-group">
                            <h3>1. Votre situation familiale</h3>
                            <div class="input-group">
                                <label>Situation familiale</label>
                                <select id="familySituation">
                                    <option value="single">Célibataire</option>
                                    <option value="married">Marié/Pacsé</option>
                                    <option value="divorced">Divorcé/Séparé</option>
                                    <option value="widowed">Veuf/Veuve</option>
                                </select>
                            </div>
                            
                            <div class="input-group">
                                <label>Nombre d'enfants à charge</label>
                                <input type="number" id="childrenCount" min="0" max="10" value="0">
                            </div>
                            
                            <div class="input-group" id="childrenAges" style="display: none;">
                                <label>Âges des enfants (séparés par des virgules)</label>
                                <input type="text" placeholder="Ex: 5, 8, 12">
                            </div>
                            
                            <div class="input-group">
                                <label>Votre conjoint(e) perçoit-il/elle des revenus en France ?</label>
                                <div style="display: flex; gap: var(--space-md); margin-top: var(--space-xs);">
                                    <div class="checkbox-group">
                                        <input type="radio" id="spouseIncomeYes" name="spouseIncome" value="yes">
                                        <label for="spouseIncomeYes">Oui</label>
                                    </div>
                                    <div class="checkbox-group">
                                        <input type="radio" id="spouseIncomeNo" name="spouseIncome" value="no" checked>
                                        <label for="spouseIncomeNo">Non</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <h3>2. Votre activité professionnelle</h3>
                            <div class="input-group">
                                <label>Canton de travail en Suisse</label>
                                <select id="canton">
                                    <option value="geneve">Genève</option>
                                    <option value="vaud">Vaud</option>
                                    <option value="neuchatel">Neuchâtel</option>
                                    <option value="jura">Jura</option>
                                    <option value="valais">Valais</option>
                                    <option value="basel">Bâle</option>
                                </select>
                            </div>
                            
                            <div class="input-group">
                                <label>Votre âge</label>
                                <input type="number" id="age" min="18" max="70" value="35">
                            </div>
                        </div>
                        
                        <div class="form-navigation">
                            <div></div> <!-- Spacer -->
                            <button class="btn" onclick="nextStep(2)">Suivant</button>
                        </div>
                    </div>
                    
                    <!-- Étape 2: Vos Revenus -->
                    <div class="form-step" id="step2">
                        <div class="form-group">
                            <h3>3. Vos revenus</h3>
                            <div class="input-group">
                                <label>Salaire Brut Annuel en CHF</label>
                                <input type="number" id="salary" value="80000">
                            </div>
                            
                            <div class="input-group">
                                <label>
                                    Revenu Fiscal de Référence (RFR) de l'année N-2 (2023 pour la cotisation 2025)
                                    <span class="info-tooltip">
                                        <i class="fas fa-info-circle"></i>
                                        <span class="tooltip-text">Trouvez cette information sur votre avis d'imposition français, en haut de la première page.</span>
                                    </span>
                                </label>
                                <input type="number" id="rfr" value="35000">
                            </div>
                            
                            <div class="advanced-toggle" onclick="toggleAdvanced()">
                                <i class="fas fa-cog"></i>
                                <span>Options de simulation avancée</span>
                            </div>
                            
                            <div class="advanced-options" id="advancedOptions">
                                <div class="input-group">
                                    <label>Déductions spécifiques (rachat 2ème pilier, frais réels, etc.)</label>
                                    <input type="number" id="deductions" value="0">
                                </div>
                                
                                <div class="input-group">
                                    <label>
                                        Taux de change CHF/EUR 
                                        <span class="info-tooltip">
                                            <i class="fas fa-info-circle"></i>
                                            <span class="tooltip-text">Taux officiel Banque de France. Modifiable si vous souhaitez simuler avec un taux différent.</span>
                                        </span>
                                    </label>
                                    <input type="number" id="exchangeRate" step="0.01" value="1.07">
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-navigation">
                            <button class="btn btn-secondary" onclick="prevStep(1)">Précédent</button>
                            <button class="btn" onclick="nextStep(3)">Suivant</button>
                        </div>
                    </div>
                    
                    <!-- Étape 3: Vos Besoins de Santé -->
                    <div class="form-step" id="step3">
                        <div class="form-group">
                            <h3>4. Vos besoins de santé</h3>
                            <div class="input-group">
                                <label>Où prévoyez-vous de vous faire soigner principalement ?</label>
                                <select id="healthcareLocation">
                                    <option value="france">France</option>
                                    <option value="switzerland">Suisse</option>
                                    <option value="both">Les deux indifféremment</option>
                                </select>
                            </div>
                            
                            <div class="input-group">
                                <label>Pour affiner la simulation LAMal, estimez vos dépenses de santé annuelles (consultations, pharmacie...)</label>
                                <div class="slider-container">
                                    <input type="range" min="0" max="2" value="1" class="slider" id="healthCosts">
                                    <div class="slider-labels">
                                        <span>Faibles (&lt; 300 CHF)</span>
                                        <span>Moyennes (~1000 CHF)</span>
                                        <span>Élevées (&gt; 2000 CHF)</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-navigation">
                            <button class="btn btn-secondary" onclick="prevStep(2)">Précédent</button>
                            <button class="btn" onclick="calculateResults()">Voir les résultats</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Résultats -->
    <section id="results" style="display: none;">
        <div class="container">
            <div class="results-header">
                <h2>Résultats de votre simulation</h2>
                <p>Comparaison personnalisée basée sur votre situation</p>
            </div>
            
            <div class="results-container">
                <div class="card" id="lamalCard">
                    <div class="card-header">
                        <h3>LAMal</h3>
                        <p>Assurance maladie suisse</p>
                    </div>
                    <div class="price">124€ <span class="price-period">/ mois</span></div>
                    <p>Coût annuel estimé: <strong>1,488€</strong></p>
                    <p class="details-toggle" onclick="toggleDetails('lamalDetails')">Voir le détail du calcul</p>
                    <div class="details-content" id="lamalDetails">
                        <p><strong>Détail du calcul:</strong></p>
                        <ul>
                            <li>Prime de base: 95€/mois</li>
                            <li>Franchise: 300€</li>
                            <li>Quote-part: 93€</li>
                            <li>Total annuel: 1,488€</li>
                        </ul>
                    </div>
                </div>
                
                <div class="card recommended" id="cmuCard">
                    <div class="card-header">
                        <h3>CMU</h3>
                        <p>Couverture Maladie Universelle française</p>
                    </div>
                    <div class="price">87€ <span class="price-period">/ mois</span></div>
                    <p>Coût annuel estimé: <strong>1,044€</strong></p>
                    <p class="details-toggle" onclick="toggleDetails('cmuDetails')">Voir le détail du calcul</p>
                    <div class="details-content" id="cmuDetails">
                        <p><strong>Détail du calcul:</strong></p>
                        <ul>
                            <li>RFR: 35,000€</li>
                            <li>Abattement: 5,000€</li>
                            <li>Taux: 8%</li>
                            <li>Total annuel: (35,000 - 5,000) × 8% = 1,044€</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="threshold-graph">
                <h3>À partir de quel revenu la LAMal devient-elle plus intéressante ?</h3>
                <p>Pour votre situation familiale, le seuil de basculement est de <strong>42,000€ de RFR</strong></p>
                
                <div class="graph-container">
                    <div class="threshold-marker" style="left: 65%;"></div>
                    <div class="threshold-label">Seuil: 42,000€</div>
                    <div class="current-position" style="left: 45%;">Votre RFR: 35,000€</div>
                </div>
                
                <div class="graph-labels">
                    <span>CMU avantageuse</span>
                    <span>LAMal avantageuse</span>
                </div>
            </div>
            
            <div class="comparison-table">
                <h3 style="text-align: center; margin-bottom: var(--space-md);">Comparaison des avantages et inconvénients</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Critère</th>
                            <th>LAMal</th>
                            <th>CMU</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>Couverture des soins en Suisse</td>
                            <td><i class="fas fa-check" style="color: var(--success);"></i> Excellente</td>
                            <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Limitée</td>
                        </tr>
                        <tr>
                            <td>Couverture des soins en France</td>
                            <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Sous conditions</td>
                            <td><i class="fas fa-check" style="color: var(--success);"></i> Complète</td>
                        </tr>
                        <tr>
                            <td>Couverture de la famille</td>
                            <td><i class="fas fa-times" style="color: #e53e3e;"></i> Payante par personne</td>
                            <td><i class="fas fa-check" style="color: var(--success);"></i> Incluse pour ayants droit</td>
                        </tr>
                        <tr>
                            <td>Démarches administratives</td>
                            <td><i class="fas fa-check" style="color: var(--success);"></i> Simplifiées en Suisse</td>
                            <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Complexes pour remboursements</td>
                        </tr>
                        <tr>
                            <td>Impact des hauts revenus</td>
                            <td><i class="fas fa-check" style="color: var(--success);"></i> Prime fixe, indépendante du revenu</td>
                            <td><i class="fas fa-exclamation-triangle" style="color: var(--warning);"></i> Contribution proportionnelle au RFR</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="card" style="background: #f0f7ff; border-left: 4px solid var(--primary); margin-top: var(--space-xl);">
                <h3>Recommandation personnalisée</h3>
                <p>Selon votre profil (famille avec conjoint sans revenus en France), <strong>la CMU présente un avantage financier notable</strong> avec une économie annuelle d'environ 444€.</p>
                <p>Pour vos besoins de santé principalement en France, la CMU offre également une meilleure couverture et des démarches simplifiées.</p>
            </div>
            
            <div style="text-align: center; margin-top: var(--space-xl);">
                <button class="btn" onclick="showReport()">Télécharger mon rapport personnalisé (PDF)</button>
            </div>
        </div>
    </section>

    <!-- Rapport -->
    <section id="report" style="display: none;">
        <div class="container">
            <div class="report-container">
                <h2 style="margin-bottom: var(--space-md);">Votre rapport personnalisé est prêt !</h2>
                <p>Téléchargez votre analyse complète pour prendre une décision éclairée</p>
                
                <div class="report-card">
                    <i class="fas fa-file-pdf" style="font-size: 4rem; color: var(--accent); margin-bottom: var(--space-md);"></i>
                    <h3 style="margin-bottom: var(--space-sm);">Rapport d'analyse LAMal vs. CMU</h3>
                    <p style="margin-bottom: var(--space-lg);">Personnalisé pour <strong>Martin Dupont</strong> - Généré le 15/07/2025</p>
                    
                    <a href="#" class="btn" style="margin-bottom: var(--space-xl);">
                        <i class="fas fa-download"></i> Télécharger le rapport (PDF)
                    </a>
                    
                    <div class="warning">
                        <p><strong>Avertissement :</strong> Cette simulation a une valeur informative et ne constitue pas un conseil personnalisé. Les résultats sont basés sur les informations fournies et les données officielles disponibles en juillet 2025. Pour une analyse précise de votre situation, consultez un conseiller spécialisé.</p>
                    </div>
                </div>
                
                <div style="margin-top: var(--space-xl);">
                    <h3 style="text-align: center; margin-bottom: var(--space-lg);">Besoin d'aller plus loin ?</h3>
                    <p style="text-align: center; max-width: 700px; margin: 0 auto var(--space-xl);">Consultez un conseiller spécialisé pour une analyse approfondie de votre situation et bénéficiez d'un accompagnement personnalisé dans votre choix d'assurance maladie.</p>
                    
                    <div class="partners">
                        <img src="https://placeholder.pics/svg/150x60/EEE/555555/GTE" alt="GTE" class="partner-logo">
                        <img src="https://placeholder.pics/svg/150x60/EEE/555555/Conseiller-Fiscal" alt="Conseiller Fiscal" class="partner-logo">
                        <img src="https://placeholder.pics/svg/150x60/EEE/555555/Assurance-Expert" alt="Assurance Expert" class="partner-logo">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ -->
    <section id="faq" style="background: #f8fafc; padding: var(--space-xl) 0;">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: var(--space-xl);">Foire Aux Questions</h2>
            
            <div class="faq-container">
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Quelle est la différence entre la LAMal et la CMU ?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>La LAMal (Loi sur l'Assurance Maladie) est le système d'assurance maladie obligatoire en Suisse. La CMU (Couverture Maladie Universelle) est le système français. En tant que frontalier, vous pouvez choisir entre les deux systèmes, mais ce choix est annuel et irrévocable.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Où trouver mon Revenu Fiscal de Référence (RFR) ?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Votre RFR figure sur votre avis d'imposition français, en haut de la première page. Il correspond à votre revenu net imposable après abattements. Pour la cotisation 2025, on utilise le RFR de l'année 2023 (N-2).</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Puis-je changer d'option en cours d'année ?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>Non, votre choix est annuel et irrévocable. Vous devez faire votre déclaration avant le 30 novembre pour l'année suivante. Les seules exceptions concernent des changements majeurs de situation (mariage, naissance, perte d'emploi).</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Comment est calculée la contribution CMU ?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>La contribution CMU est calculée comme suit : (Revenu Fiscal de Référence - Abattement) × Taux (8% en 2025). L'abattement est de 8,922€ pour une personne seule et augmente selon votre situation familiale.</p>
                    </div>
                </div>
                
                <div class="faq-item">
                    <div class="faq-question" onclick="toggleFAQ(this)">
                        <span>Quelle option est préférable pour une famille ?</span>
                        <i class="fas fa-chevron-down"></i>
                    </div>
                    <div class="faq-answer">
                        <p>La CMU présente souvent un avantage financier pour les familles car elle couvre gratuitement les enfants et le conjoint sans revenus. Avec la LAMal, chaque membre de la famille doit souscrire une assurance séparée, ce qui augmente significativement le coût.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="footer-container">
            <div class="footer-about">
                <a href="#" class="footer-logo">FrontalierAssurance</a>
                <p>Simulateur indépendant pour aider les travailleurs frontaliers à choisir leur assurance maladie entre la LAMal suisse et la CMU française.</p>
            </div>
            
            <div class="footer-links">
                <h3>Liens utiles</h3>
                <ul>
                    <li><a href="#">Simulateur</a></li>
                    <li><a href="#faq">FAQ</a></li>
                    <li><a href="#">À propos</a></li>
                    <li><a href="#">Mentions légales</a></li>
                    <li><a href="#">Politique de confidentialité</a></li>
                </ul>
            </div>
            
            <div class="footer-links">
                <h3>Sources officielles</h3>
                <ul>
                    <li><a href="#">OFSP (Suisse)</a></li>
                    <li><a href="#">Ameli (France)</a></li>
                    <li><a href="#">GTE Frontaliers</a></li>
                    <li><a href="#">Impôts.gouv.fr</a></li>
                </ul>
            </div>
        </div>
        
        <div class="copyright container">
            <p>&copy; 2025 FrontalierAssurance - Tous droits réservés. Données officielles 2025.</p>
        </div>
    </footer>

    <script>
        // Navigation mobile
        const menuToggle = document.getElementById('menuToggle');
        const mainNav = document.getElementById('mainNav');
        
        menuToggle.addEventListener('click', () => {
            mainNav.classList.toggle('active');
        });
        
        // Gestion des étapes du formulaire
        function nextStep(step) {
            document.querySelector('.form-step.active').classList.remove('active');
            document.getElementById(`step${step}`).classList.add('active');
            
            // Mise à jour de la barre de progression
            const progress = document.getElementById('formProgress');
            progress.style.width = `${step * 33}%`;
            
            // Faire défiler jusqu'au formulaire
            document.getElementById('simulator').scrollIntoView({ behavior: 'smooth' });
        }
        
        function prevStep(step) {
            document.querySelector('.form-step.active').classList.remove('active');
            document.getElementById(`step${step}`).classList.add('active');
            
            // Mise à jour de la barre de progression
            const progress = document.getElementById('formProgress');
            progress.style.width = `${step * 33}%`;
        }
        
        // Affichage des options avancées
        function toggleAdvanced() {
            const advancedOptions = document.getElementById('advancedOptions');
            const toggleIcon = document.querySelector('.advanced-toggle i');
            
            if (advancedOptions.style.display === 'block') {
                advancedOptions.style.display = 'none';
                toggleIcon.className = 'fas fa-cog';
            } else {
                advancedOptions.style.display = 'block';
                toggleIcon.className = 'fas fa-times';
            }
        }
        
        // Affichage des détails des résultats
        function toggleDetails(id) {
            const details = document.getElementById(id);
            if (details.style.display === 'block') {
                details.style.display = 'none';
            } else {
                details.style.display = 'block';
            }
        }
        
        // Calcul des résultats
        function calculateResults() {
            // Simulation de calcul
            document.getElementById('simulator').style.display = 'none';
            document.getElementById('results').style.display = 'block';
            document.getElementById('results').scrollIntoView({ behavior: 'smooth' });
        }
        
        // Affichage du rapport
        function showReport() {
            document.getElementById('results').style.display = 'none';
            document.getElementById('report').style.display = 'block';
            document.getElementById('report').scrollIntoView({ behavior: 'smooth' });
        }
        
        // Gestion de la FAQ
        function toggleFAQ(element) {
            const faqItem = element.parentElement;
            faqItem.classList.toggle('active');
            
            const icon = element.querySelector('i');
            if (faqItem.classList.contains('active')) {
                icon.className = 'fas fa-chevron-up';
            } else {
                icon.className = 'fas fa-chevron-down';
            }
        }
        
        // Affichage des âges des enfants si nécessaire
        document.getElementById('childrenCount').addEventListener('change', function() {
            const childrenAges = document.getElementById('childrenAges');
            if (this.value > 0) {
                childrenAges.style.display = 'block';
            } else {
                childrenAges.style.display = 'none';
            }
        });
    </script>
</body>
</html>
